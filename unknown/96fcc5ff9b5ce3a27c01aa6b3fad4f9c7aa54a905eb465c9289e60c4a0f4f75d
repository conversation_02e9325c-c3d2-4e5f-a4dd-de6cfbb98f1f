#!/usr/bin/env python3
"""
Comprehensive test for complete real data system - OTC and Live pairs
"""

import asyncio
import sys
import os
import time

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from Model import fetch_quotex_market_data, generate_signal, QUOTEX_OTC_PAIRS, QUOTEX_LIVE_PAIRS
from utils import print_colored, fetch_live_candles
from strategy_engine import StrategyEngine

async def test_otc_real_data_fetching():
    """Test OTC pairs fetch real data from Quotex WebSocket"""
    print_colored("🧪 Testing OTC Real Data Fetching", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    # Test a few key OTC pairs
    test_otc_pairs = ["EURUSD_otc", "USDJPY_otc", "GBPUSD_otc", "XAUUSD_otc"]
    success_count = 0
    
    for pair in test_otc_pairs:
        print_colored(f"\n🔍 Testing {pair} (OTC pair - Quotex WebSocket)...", "INFO")
        
        try:
            # Test with different candle counts
            for candle_count in [10, 20, 50]:
                print_colored(f"  📊 Testing with {candle_count} candles...", "INFO")
                
                df = await fetch_quotex_market_data(pair, "M1", candle_count)
                
                if df is not None and len(df) > 0:
                    latest_price = df['close'].iloc[-1]
                    print_colored(f"  ✅ {pair}: {len(df)} real candles (requested {candle_count}), price: {latest_price:.5f}", "SUCCESS")
                    success_count += 1
                    break  # Success with this pair
                else:
                    print_colored(f"  ⚠️ {pair}: No real data for {candle_count} candles", "WARNING")
                    
        except Exception as e:
            print_colored(f"  ❌ {pair}: Error - {str(e)}", "ERROR")
    
    print_colored(f"\n📊 OTC pairs test: {success_count}/{len(test_otc_pairs)} successful", 
                 "SUCCESS" if success_count > 0 else "WARNING")
    
    return success_count > 0

async def test_live_real_data_fetching():
    """Test Live pairs fetch real data from Oanda API"""
    print_colored("\n🧪 Testing Live Real Data Fetching", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    # Test key Live pairs
    test_live_pairs = ["USDJPY", "EURUSD", "GBPUSD", "AUDUSD"]
    success_count = 0
    
    for pair in test_live_pairs:
        print_colored(f"\n🔍 Testing {pair} (Live pair - Oanda API)...", "INFO")
        
        try:
            # Test with different candle counts
            for candle_count in [10, 20, 50]:
                print_colored(f"  📊 Testing with {candle_count} candles...", "INFO")
                
                df = await fetch_quotex_market_data(pair, "M1", candle_count)
                
                if df is not None and len(df) > 0:
                    latest_price = df['close'].iloc[-1]
                    print_colored(f"  ✅ {pair}: {len(df)} real candles (requested {candle_count}), price: {latest_price:.5f}", "SUCCESS")
                    success_count += 1
                    break  # Success with this pair
                else:
                    print_colored(f"  ⚠️ {pair}: No real data for {candle_count} candles", "WARNING")
                    
        except Exception as e:
            print_colored(f"  ❌ {pair}: Error - {str(e)}", "ERROR")
    
    print_colored(f"\n📊 Live pairs test: {success_count}/{len(test_live_pairs)} successful", 
                 "SUCCESS" if success_count > 0 else "WARNING")
    
    return success_count > 0

async def test_dynamic_strategy_engine():
    """Test strategy engine works with different candle counts"""
    print_colored("\n🎯 Testing Dynamic Strategy Engine", "HEADER", bold=True)
    print_colored("=" * 40, "HEADER")
    
    strategy_engine = StrategyEngine()
    test_pairs = ["USDJPY", "EURUSD"]  # Use Live pairs for reliable data
    candle_counts = [10, 20, 30, 50]
    
    success_count = 0
    total_tests = 0
    
    for pair in test_pairs:
        for candle_count in candle_counts:
            total_tests += 1
            print_colored(f"\n🔍 Testing {pair} with {candle_count} candles...", "INFO")
            
            try:
                # Get real data
                df = await fetch_quotex_market_data(pair, "M1", candle_count)
                
                if df is not None and len(df) > 0:
                    # Test strategy with this data
                    signal, confidence = strategy_engine.evaluate_strategy_4(df)
                    
                    if signal != 0 or confidence >= 0:  # Strategy executed successfully
                        signal_text = "CALL" if signal == 1 else ("PUT" if signal == -1 else "HOLD")
                        print_colored(f"  ✅ {pair} ({len(df)} candles): Signal={signal_text}, Confidence={confidence:.1%}", "SUCCESS")
                        success_count += 1
                    else:
                        print_colored(f"  ⚠️ {pair} ({len(df)} candles): Strategy failed to execute", "WARNING")
                else:
                    print_colored(f"  ❌ {pair}: No real data available", "ERROR")
                    
            except Exception as e:
                print_colored(f"  ❌ {pair} ({candle_count} candles): Error - {str(e)}", "ERROR")
    
    print_colored(f"\n📊 Dynamic strategy test: {success_count}/{total_tests} successful", 
                 "SUCCESS" if success_count > total_tests // 2 else "WARNING")
    
    return success_count > total_tests // 2

async def test_signal_generation_with_real_data():
    """Test complete signal generation with real data only"""
    print_colored("\n🚀 Testing Complete Signal Generation", "HEADER", bold=True)
    print_colored("=" * 45, "HEADER")
    
    strategy_engine = StrategyEngine()
    test_pairs = [
        ("USDJPY", "Live"),
        ("EURUSD", "Live"),
        ("EURUSD_otc", "OTC"),
        ("USDJPY_otc", "OTC")
    ]
    
    signals_generated = 0
    total_tests = len(test_pairs)
    
    for pair, pair_type in test_pairs:
        print_colored(f"\n🔍 Testing complete signal generation for {pair} ({pair_type})...", "INFO")
        
        try:
            # Test with different candle counts
            for candle_count in [15, 25, 40]:
                signal, confidence, price, strategy = await generate_signal(
                    pair, strategy_engine, ["S4"], "M1", 10, candle_count
                )
                
                if price > 0:  # Real data was used
                    if signal != "hold":
                        print_colored(f"  ✅ {pair} ({candle_count} candles): {signal.upper()}, {confidence:.1%}, ${price:.5f}, {strategy}", "SUCCESS")
                        signals_generated += 1
                        break
                    else:
                        print_colored(f"  📊 {pair} ({candle_count} candles): Real data used, signal=HOLD, price=${price:.5f}", "INFO")
                        signals_generated += 1  # Still counts as success (real data used)
                        break
                else:
                    print_colored(f"  ⚠️ {pair} ({candle_count} candles): No real data available", "WARNING")
                    
        except Exception as e:
            print_colored(f"  ❌ {pair}: Signal generation error - {str(e)}", "ERROR")
    
    print_colored(f"\n📊 Signal generation test: {signals_generated}/{total_tests} successful", 
                 "SUCCESS" if signals_generated > 0 else "WARNING")
    
    return signals_generated > 0

async def test_no_fallback_data_policy():
    """Test that no fallback data is ever used"""
    print_colored("\n🚫 Testing No Fallback Data Policy", "HEADER", bold=True)
    print_colored("=" * 40, "HEADER")
    
    # Test with fake pairs to ensure no fallback
    fake_pairs = ["FAKEPAIR_otc", "NONEXISTENT", "INVALID_otc"]
    
    for fake_pair in fake_pairs:
        print_colored(f"\n🔍 Testing fake pair: {fake_pair}", "INFO")
        
        try:
            df = await fetch_quotex_market_data(fake_pair, "M1", 20)
            
            if df is None:
                print_colored(f"  ✅ {fake_pair}: Correctly returned None (no fallback data)", "SUCCESS")
            else:
                print_colored(f"  ❌ {fake_pair}: ERROR - Fallback data was generated!", "ERROR")
                return False
                
        except Exception as e:
            print_colored(f"  ✅ {fake_pair}: Correctly failed - {str(e)}", "SUCCESS")
    
    print_colored("\n✅ No fallback data policy: PASSED", "SUCCESS")
    return True

async def main():
    """Run all comprehensive real data system tests"""
    print_colored("🚀 COMPLETE REAL DATA SYSTEM TEST", "CYAN", bold=True)
    print_colored("=" * 80, "CYAN", bold=True)
    print_colored("🎯 Testing OTC pairs (Quotex WebSocket) + Live pairs (Oanda API)", "CYAN")
    print_colored("📊 Testing dynamic strategy engine with various candle counts", "CYAN")
    print_colored("🚫 Verifying NO fallback/synthetic data is ever used", "CYAN")
    print_colored("=" * 80, "CYAN", bold=True)
    
    tests = [
        ("Live Pairs Real Data", test_live_real_data_fetching),
        ("OTC Pairs Real Data", test_otc_real_data_fetching),
        ("Dynamic Strategy Engine", test_dynamic_strategy_engine),
        ("Complete Signal Generation", test_signal_generation_with_real_data),
        ("No Fallback Policy", test_no_fallback_data_policy),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print_colored(f"\n{'='*25} {test_name} {'='*25}", "CYAN")
            if await test_func():
                passed += 1
                print_colored(f"✅ {test_name}: PASSED", "SUCCESS", bold=True)
            else:
                print_colored(f"❌ {test_name}: FAILED", "ERROR", bold=True)
        except Exception as e:
            print_colored(f"❌ {test_name}: CRASHED - {e}", "ERROR", bold=True)
    
    print_colored("\n" + "=" * 80, "CYAN", bold=True)
    print_colored(f"📊 FINAL RESULTS: {passed}/{total} TESTS PASSED", "CYAN", bold=True)
    
    if passed == total:
        print_colored("🎉 ALL TESTS PASSED - COMPLETE REAL DATA SYSTEM WORKING!", "SUCCESS", bold=True)
        print_colored("✅ OTC pairs use REAL Quotex WebSocket data", "SUCCESS")
        print_colored("✅ Live pairs use REAL Oanda API data", "SUCCESS")
        print_colored("✅ Strategy engine works with any candle count", "SUCCESS")
        print_colored("✅ No synthetic/fallback data is ever used", "SUCCESS")
        print_colored("✅ Signal generation requires real data only", "SUCCESS")
        print_colored("🚀 READY FOR LIVE TRADING WITH 100% REAL DATA!", "SUCCESS", bold=True)
    else:
        print_colored("⚠️ SOME TESTS FAILED - ISSUES DETECTED", "ERROR", bold=True)
        print_colored("🔧 Fix required before using for live trading", "WARNING")
    
    print_colored("=" * 80, "CYAN", bold=True)
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())

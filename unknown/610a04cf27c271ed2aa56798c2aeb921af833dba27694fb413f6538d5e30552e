#!/usr/bin/env python3
"""
Test script to verify multiple click methods for disabled elements
"""

import asyncio
import sys
import os

# Add the parent directory to the path to import modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from quotex_integration import QuotexBotIntegration
from utils import print_colored

async def test_click_methods():
    """Test multiple click methods for the time switch button"""
    
    print_colored("🧪 Testing Multiple Click Methods for Disabled Elements", "HEADER", bold=True)
    print_colored("=" * 70, "HEADER")
    print_colored("🎯 Testing force, JavaScript, and dispatch click methods", "INFO")
    print_colored("=" * 70, "HEADER")
    
    # Get credentials from user
    print_colored("📝 Please enter your Quotex credentials:", "INFO")
    email = input("Email: ").strip()
    password = input("Password: ").strip()
    
    if not email or not password:
        print_colored("❌ Email and password are required!", "ERROR")
        return False
    
    try:
        # Initialize the integration
        print_colored("🔧 Initializing Quotex integration...", "INFO")
        quotex = QuotexBotIntegration(email, password, demo_mode=True)
        
        # Connect to Quotex
        print_colored("📡 Connecting to Quotex...", "INFO")
        connection_success = await quotex.connect()
        
        if not connection_success:
            print_colored("❌ Failed to connect to Quotex!", "ERROR")
            return False
        
        print_colored("✅ Successfully connected to Quotex!", "SUCCESS")
        await asyncio.sleep(3)
        
        # Test finding the time switch element
        print_colored("🔍 Finding time switch element...", "INFO")
        
        # Use exact class selector from HTML
        time_switch_selectors = [
            'span.input-control__label__switch',
            '.input-control__label__switch',
            'span:has-text("Switch time")',
            'span:has-text("Tempo de comutação")',
        ]
        
        time_switch_element = None
        
        for selector in time_switch_selectors:
            try:
                elements = await quotex.page.query_selector_all(selector)
                print_colored(f"   Testing selector: {selector} - Found {len(elements)} elements", "INFO")
                
                for element in elements:
                    if await element.is_visible():
                        text = (await element.text_content() or '').strip()
                        if text and ('switch' in text.lower() or 'tempo' in text.lower()):
                            print_colored(f"   Found element with text: '{text}'", "SUCCESS")
                            time_switch_element = element
                            break
                
                if time_switch_element:
                    break
                    
            except Exception as e:
                print_colored(f"   Error with selector {selector}: {e}", "WARNING")
        
        if not time_switch_element:
            print_colored("❌ Time switch element not found!", "ERROR")
            return False
        
        print_colored("✅ Time switch element found!", "SUCCESS")
        
        # Test element properties
        print_colored("📊 Testing element properties...", "INFO")
        
        try:
            is_visible = await time_switch_element.is_visible()
            is_enabled = await time_switch_element.is_enabled()
            bounding_box = await time_switch_element.bounding_box()
            
            print_colored(f"   Visible: {is_visible}", "SUCCESS" if is_visible else "WARNING")
            print_colored(f"   Enabled: {is_enabled}", "SUCCESS" if is_enabled else "WARNING")
            
            if bounding_box:
                print_colored(f"   Position: ({bounding_box['x']}, {bounding_box['y']})", "INFO")
                print_colored(f"   Size: {bounding_box['width']}x{bounding_box['height']}", "INFO")
            
        except Exception as e:
            print_colored(f"   Error checking properties: {e}", "WARNING")
        
        # Test multiple click methods
        print_colored("🔘 Testing multiple click methods...", "INFO")
        
        click_methods = [
            ("Standard Click", lambda: time_switch_element.click()),
            ("Force Click", lambda: time_switch_element.click(force=True)),
            ("JavaScript Click", lambda: time_switch_element.evaluate('el => el.click()')),
            ("Dispatch Click", lambda: time_switch_element.dispatch_event('click')),
            ("Mouse Click", lambda: time_switch_element.click(button='left')),
        ]
        
        for method_name, click_method in click_methods:
            print_colored(f"   Testing {method_name}...", "INFO")
            
            try:
                # Get current time format before click
                try:
                    time_input = await quotex.page.query_selector('input[id*="time"]')
                    before_value = await time_input.get_attribute('value') if time_input else "unknown"
                    before_placeholder = await time_input.get_attribute('placeholder') if time_input else "unknown"
                except:
                    before_value = "unknown"
                    before_placeholder = "unknown"
                
                print_colored(f"      Before: value='{before_value}', placeholder='{before_placeholder}'", "INFO")
                
                # Try the click method
                await click_method()
                await asyncio.sleep(2)  # Wait for UI to update
                
                # Check if time format changed
                try:
                    time_input = await quotex.page.query_selector('input[id*="time"]')
                    after_value = await time_input.get_attribute('value') if time_input else "unknown"
                    after_placeholder = await time_input.get_attribute('placeholder') if time_input else "unknown"
                except:
                    after_value = "unknown"
                    after_placeholder = "unknown"
                
                print_colored(f"      After: value='{after_value}', placeholder='{after_placeholder}'", "INFO")
                
                # Check if format changed (seconds appeared)
                format_changed = (
                    (before_placeholder != after_placeholder) or
                    (len(after_placeholder.split(':')) == 3 and len(before_placeholder.split(':')) == 2) or
                    (len(after_value.split(':')) == 3 and len(before_value.split(':')) == 2)
                )
                
                if format_changed:
                    print_colored(f"   ✅ {method_name} SUCCESS - Time format changed!", "SUCCESS")
                    print_colored(f"      Format switched to include seconds!", "SUCCESS")
                    return True
                else:
                    print_colored(f"   ⚠️ {method_name} - No format change detected", "WARNING")
                
            except Exception as click_error:
                print_colored(f"   ❌ {method_name} failed: {click_error}", "ERROR")
            
            # Wait between attempts
            await asyncio.sleep(1)
        
        # Test alternative approach: Look for parent clickable element
        print_colored("🔍 Testing parent element clicking...", "INFO")
        
        try:
            # Get parent elements that might be clickable
            parent = await time_switch_element.evaluate('el => el.parentElement')
            if parent:
                print_colored("   Found parent element, testing click...", "INFO")
                
                try:
                    await parent.click(force=True)
                    await asyncio.sleep(2)
                    print_colored("   ✅ Parent element clicked successfully!", "SUCCESS")
                except Exception as parent_error:
                    print_colored(f"   ❌ Parent click failed: {parent_error}", "ERROR")
                    
        except Exception as e:
            print_colored(f"   Error with parent element: {e}", "WARNING")
        
        print_colored("=" * 70, "HEADER")
        print_colored("🎉 Click method testing completed!", "SUCCESS", bold=True)
        print_colored("=" * 70, "HEADER")
        
        # Keep browser open for manual inspection
        print_colored("🔍 Browser will stay open for 20 seconds for manual inspection...", "INFO")
        print_colored("You can manually try clicking the time switch button.", "INFO")
        
        await asyncio.sleep(20)
        
        return True
        
    except Exception as e:
        print_colored(f"❌ Test failed with error: {e}", "ERROR")
        return False
    
    finally:
        # Clean up
        try:
            if 'quotex' in locals() and quotex.page:
                await quotex.page.close()
            if 'quotex' in locals() and quotex.browser:
                await quotex.browser.close()
        except:
            pass

def main():
    """Main function to run the click method test"""
    try:
        # Run the async test
        result = asyncio.run(test_click_methods())
        
        if result:
            print_colored("\n✅ Click method test completed!", "SUCCESS", bold=True)
        else:
            print_colored("\n❌ Click method test failed. Check the errors above.", "ERROR", bold=True)
            
    except KeyboardInterrupt:
        print_colored("\n⚠️ Test interrupted by user", "WARNING")
    except Exception as e:
        print_colored(f"\n❌ Unexpected error: {e}", "ERROR")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test Oanda API connectivity and data fetching
"""

import requests
import sys
import os
import json
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from config import OANDA_CONFIG
from utils import print_colored, get_oanda_headers, fetch_live_candles

def test_oanda_connection():
    """Test basic Oanda API connection"""
    print_colored("🧪 Testing Oanda API Connection", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    try:
        # Test account info endpoint
        headers = get_oanda_headers()
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/accounts/{OANDA_CONFIG['ACCOUNT_ID']}"
        
        print_colored(f"🔗 Testing connection to: {OANDA_CONFIG['BASE_URL']}", "INFO")
        print_colored(f"🔑 Using account: {OANDA_CONFIG['ACCOUNT_ID']}", "INFO")
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            account_data = response.json()
            print_colored("✅ Oanda API connection successful!", "SUCCESS")
            print_colored(f"📊 Account currency: {account_data['account']['currency']}", "SUCCESS")
            print_colored(f"💰 Account balance: {account_data['account']['balance']}", "SUCCESS")
            return True
        else:
            print_colored(f"❌ API connection failed: {response.status_code}", "ERROR")
            print_colored(f"📄 Response: {response.text}", "ERROR")
            return False
            
    except Exception as e:
        print_colored(f"❌ Connection test failed: {e}", "ERROR")
        return False

def test_instruments_list():
    """Test fetching available instruments"""
    print_colored("\n🎯 Testing Available Instruments", "HEADER", bold=True)
    print_colored("=" * 40, "HEADER")
    
    try:
        headers = get_oanda_headers()
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/accounts/{OANDA_CONFIG['ACCOUNT_ID']}/instruments"
        
        response = requests.get(url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            instruments_data = response.json()
            instruments = instruments_data['instruments']
            
            print_colored(f"✅ Found {len(instruments)} available instruments", "SUCCESS")
            
            # Check for key currency pairs
            key_pairs = ['USD_JPY', 'EUR_USD', 'GBP_USD', 'AUD_USD']
            available_pairs = [inst['name'] for inst in instruments]
            
            for pair in key_pairs:
                if pair in available_pairs:
                    print_colored(f"✅ {pair} - Available", "SUCCESS")
                else:
                    print_colored(f"❌ {pair} - Not available", "ERROR")
            
            return True
        else:
            print_colored(f"❌ Instruments fetch failed: {response.status_code}", "ERROR")
            return False
            
    except Exception as e:
        print_colored(f"❌ Instruments test failed: {e}", "ERROR")
        return False

def test_live_data_fetch():
    """Test fetching live candle data for key pairs"""
    print_colored("\n📊 Testing Live Data Fetching", "HEADER", bold=True)
    print_colored("=" * 40, "HEADER")
    
    test_pairs = ['USD_JPY', 'EUR_USD', 'GBP_USD']
    success_count = 0
    
    for pair in test_pairs:
        print_colored(f"\n🔍 Testing {pair}...", "INFO")
        
        try:
            # Test with small count first
            df = fetch_live_candles(pair, count=10, granularity="M1")
            
            if df is not None and len(df) > 0:
                latest_price = df['close'].iloc[-1]
                timestamp = df.index[-1] if hasattr(df.index[-1], 'strftime') else 'N/A'
                
                print_colored(f"✅ {pair}: {len(df)} candles fetched", "SUCCESS")
                print_colored(f"💰 Latest price: {latest_price:.5f}", "SUCCESS")
                print_colored(f"🕐 Latest time: {timestamp}", "SUCCESS")
                success_count += 1
            else:
                print_colored(f"❌ {pair}: No data returned", "ERROR")
                
        except Exception as e:
            print_colored(f"❌ {pair}: Error - {str(e)}", "ERROR")
    
    print_colored(f"\n📊 Data fetch results: {success_count}/{len(test_pairs)} successful", 
                 "SUCCESS" if success_count == len(test_pairs) else "WARNING")
    
    return success_count == len(test_pairs)

def test_api_rate_limits():
    """Test API rate limits and response times"""
    print_colored("\n⚡ Testing API Performance", "HEADER", bold=True)
    print_colored("=" * 30, "HEADER")
    
    try:
        import time
        
        # Test multiple rapid requests
        headers = get_oanda_headers()
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/instruments/USD_JPY/candles"
        params = {"count": 5, "granularity": "M1", "price": "MBA"}
        
        response_times = []
        success_count = 0
        
        for i in range(5):
            start_time = time.time()
            response = requests.get(url, headers=headers, params=params, timeout=10)
            end_time = time.time()
            
            response_time = end_time - start_time
            response_times.append(response_time)
            
            if response.status_code == 200:
                success_count += 1
                print_colored(f"✅ Request {i+1}: {response_time:.2f}s", "SUCCESS")
            else:
                print_colored(f"❌ Request {i+1}: Failed ({response.status_code})", "ERROR")
            
            time.sleep(0.5)  # Small delay between requests
        
        avg_response_time = sum(response_times) / len(response_times)
        print_colored(f"\n📊 Performance Summary:", "INFO")
        print_colored(f"✅ Success rate: {success_count}/5", "SUCCESS" if success_count == 5 else "WARNING")
        print_colored(f"⚡ Average response time: {avg_response_time:.2f}s", "INFO")
        
        return success_count >= 4  # Allow 1 failure
        
    except Exception as e:
        print_colored(f"❌ Performance test failed: {e}", "ERROR")
        return False

def main():
    """Run all Oanda API tests"""
    print_colored("🚀 OANDA API COMPREHENSIVE TEST", "CYAN", bold=True)
    print_colored("=" * 60, "CYAN", bold=True)
    
    tests = [
        ("API Connection", test_oanda_connection),
        ("Instruments List", test_instruments_list),
        ("Live Data Fetch", test_live_data_fetch),
        ("API Performance", test_api_rate_limits),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print_colored(f"❌ {test_name} test crashed: {e}", "ERROR")
    
    print_colored("\n" + "=" * 60, "CYAN", bold=True)
    print_colored(f"📊 TEST RESULTS: {passed}/{total} PASSED", "CYAN", bold=True)
    
    if passed == total:
        print_colored("🎉 ALL TESTS PASSED - OANDA API IS WORKING!", "SUCCESS", bold=True)
        print_colored("✅ Real market data is available", "SUCCESS")
        print_colored("✅ API connection is stable", "SUCCESS")
        print_colored("✅ Ready for live trading signals", "SUCCESS")
    else:
        print_colored("⚠️ SOME TESTS FAILED - API ISSUES DETECTED", "ERROR", bold=True)
        print_colored("🔧 Fix required before using real data", "WARNING")
    
    print_colored("=" * 60, "CYAN", bold=True)
    
    return passed == total

if __name__ == "__main__":
    main()

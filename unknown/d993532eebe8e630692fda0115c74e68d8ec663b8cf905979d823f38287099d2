#!/usr/bin/env python3
"""
Authentication UI Module
Handles secure access authentication interface
"""

import getpass
import sys
import os

# Add secret folder to path
secret_path = os.path.join(os.path.dirname(__file__), 'secret')
sys.path.insert(0, secret_path)

from auth_manager import auth_manager
from utils import print_colored

class AuthenticationUI:
    """Handles authentication user interface"""
    
    def __init__(self):
        self.max_attempts = 3
        self.current_attempts = 0
    
    def show_auth_header(self):
        """Display authentication header"""
        print_colored("🚀 QUOTEX TRADING BOT - SECURE ACCESS", "CYAN", bold=True)
        print_colored("=" * 60, "CYAN", bold=True)
        print_colored("🔐 SECURE ACCESS AUTHENTICATION", "CYAN", bold=True)
        print_colored("=" * 50, "CYAN", bold=True)
    
    def get_secret_input(self) -> str:
        """Get secret key input from user"""
        print_colored("🔑 Enter secret access key:", "INFO")
        
        try:
            # Use getpass to hide input
            secret = getpass.getpass("Secret Key: ")
            return secret.strip()
        except KeyboardInterrupt:
            print_colored("\n❌ Authentication cancelled by user", "ERROR")
            sys.exit(0)
        except Exception:
            return ""
    
    def show_masked_input(self, secret: str):
        """Show masked version of input"""
        masked = auth_manager.get_masked_secret(secret)
        print_colored(f"Secret Key: {masked}", "WHITE")

    @property
    def auth_manager(self):
        """Access to auth manager for testing"""
        return auth_manager
    
    def authenticate(self) -> bool:
        """Main authentication process"""
        self.show_auth_header()
        
        while self.current_attempts < self.max_attempts:
            secret = self.get_secret_input()
            
            if not secret:
                print_colored("❌ Empty secret key entered", "ERROR")
                self.current_attempts += 1
                continue
            
            # Show masked input
            self.show_masked_input(secret)
            
            # Validate secret
            if auth_manager.validate_secret(secret):
                self.show_success()
                return True
            else:
                self.show_failure()
                self.current_attempts += 1
                
                if self.current_attempts < self.max_attempts:
                    remaining = self.max_attempts - self.current_attempts
                    print_colored(f"⚠️ {remaining} attempt(s) remaining", "WARNING")
                    print()
        
        self.show_lockout()
        return False
    
    def show_success(self):
        """Display successful authentication"""
        print_colored("✅ Authentication successful!", "SUCCESS")
        print_colored("🎉 Access granted to Quotex Trading Model", "SUCCESS")
        print()
    
    def show_failure(self):
        """Display authentication failure"""
        print_colored("❌ Authentication failed!", "ERROR")
        print_colored("🔒 Invalid secret key", "ERROR")
        print()
    
    def show_lockout(self):
        """Display lockout message"""
        print_colored("🚫 Maximum authentication attempts exceeded", "ERROR")
        print_colored("🔒 Access denied - System locked", "ERROR")
        print_colored("💡 Contact system administrator for assistance", "WARNING")

# Global instance
auth_ui = AuthenticationUI()

#!/usr/bin/env python3
"""
Secure Authentication Manager
Handles encrypted secret key storage and validation
"""

import os
import hashlib
import base64
from cryptography.fernet import Fe<PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class SecureAuthManager:
    """Manages secure authentication with encrypted secret storage"""
    
    def __init__(self):
        self.secret_dir = os.path.dirname(os.path.abspath(__file__))
        self.key_file = os.path.join(self.secret_dir, '.auth_key')
        self.hash_file = os.path.join(self.secret_dir, '.auth_hash')
        self._ensure_secret_files()
    
    def _ensure_secret_files(self):
        """Ensure secret files exist with encrypted data"""
        if not os.path.exists(self.key_file) or not os.path.exists(self.hash_file):
            self._create_encrypted_secret()
    
    def _generate_key_from_password(self, password: str, salt: bytes) -> bytes:
        """Generate encryption key from password"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return base64.urlsafe_b64encode(kdf.derive(password.encode()))
    
    def _create_encrypted_secret(self):
        """Create encrypted secret files"""
        # The actual secret (this will be encrypted and stored)
        actual_secret = "miketester2390"
        
        # Generate salt for encryption
        salt = os.urandom(16)
        
        # Create encryption key from a system-based password
        system_password = "qt_auth_sys_2024"
        key = self._generate_key_from_password(system_password, salt)
        
        # Encrypt the secret
        fernet = Fernet(key)
        encrypted_secret = fernet.encrypt(actual_secret.encode())
        
        # Store encrypted data and salt
        with open(self.key_file, 'wb') as f:
            f.write(salt + encrypted_secret)
        
        # Create hash for quick validation
        secret_hash = hashlib.sha256(actual_secret.encode()).hexdigest()
        with open(self.hash_file, 'w') as f:
            f.write(secret_hash)
    
    def _decrypt_secret(self) -> str:
        """Decrypt the stored secret"""
        try:
            with open(self.key_file, 'rb') as f:
                data = f.read()
            
            # Extract salt and encrypted secret
            salt = data[:16]
            encrypted_secret = data[16:]
            
            # Generate key and decrypt
            system_password = "qt_auth_sys_2024"
            key = self._generate_key_from_password(system_password, salt)
            fernet = Fernet(key)
            
            return fernet.decrypt(encrypted_secret).decode()
        except Exception:
            return None
    
    def validate_secret(self, input_secret: str) -> bool:
        """Validate input secret against stored encrypted secret"""
        try:
            # Method 1: Hash comparison (faster)
            input_hash = hashlib.sha256(input_secret.encode()).hexdigest()
            
            if os.path.exists(self.hash_file):
                with open(self.hash_file, 'r') as f:
                    stored_hash = f.read().strip()
                
                if input_hash == stored_hash:
                    return True
            
            # Method 2: Decrypt and compare (fallback)
            stored_secret = self._decrypt_secret()
            return stored_secret == input_secret
            
        except Exception:
            return False
    
    def get_masked_secret(self, secret: str) -> str:
        """Return masked version of secret for display"""
        if len(secret) <= 4:
            return "*" * len(secret)
        return secret[:2] + "*" * (len(secret) - 4) + secret[-2:]

# Global instance
auth_manager = SecureAuthManager()

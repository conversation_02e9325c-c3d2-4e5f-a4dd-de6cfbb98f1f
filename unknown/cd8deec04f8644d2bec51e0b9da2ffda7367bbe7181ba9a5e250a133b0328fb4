#!/usr/bin/env python3
"""
Test script for the secure authentication system
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from utils import print_colored

def test_auth_manager():
    """Test the authentication manager"""
    print_colored("🧪 Testing Authentication Manager", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    try:
        # Import auth manager
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'secret'))
        from auth_manager import auth_manager
        
        print_colored("✅ Auth manager imported successfully", "SUCCESS")
        
        # Test correct secret
        correct_secret = "miketester2390"
        result = auth_manager.validate_secret(correct_secret)
        print_colored(f"🔑 Correct secret validation: {'✅ PASS' if result else '❌ FAIL'}", 
                     "SUCCESS" if result else "ERROR")
        
        # Test incorrect secret
        wrong_secret = "wrongkey123"
        result = auth_manager.validate_secret(wrong_secret)
        print_colored(f"🔒 Wrong secret validation: {'✅ PASS' if not result else '❌ FAIL'}", 
                     "SUCCESS" if not result else "ERROR")
        
        # Test masked secret
        masked = auth_manager.get_masked_secret(correct_secret)
        print_colored(f"🎭 Masked secret: {masked}", "INFO")
        
        # Test empty secret
        empty_result = auth_manager.validate_secret("")
        print_colored(f"⚪ Empty secret validation: {'✅ PASS' if not empty_result else '❌ FAIL'}", 
                     "SUCCESS" if not empty_result else "ERROR")
        
        print_colored("✅ Authentication manager tests completed", "SUCCESS")
        return True
        
    except Exception as e:
        print_colored(f"❌ Auth manager test failed: {e}", "ERROR")
        return False

def test_secret_files():
    """Test secret file creation and encryption"""
    print_colored("\n🔐 Testing Secret File System", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    try:
        secret_dir = os.path.join(os.path.dirname(__file__), 'secret')
        key_file = os.path.join(secret_dir, '.auth_key')
        hash_file = os.path.join(secret_dir, '.auth_hash')
        
        # Check if files exist
        key_exists = os.path.exists(key_file)
        hash_exists = os.path.exists(hash_file)
        
        print_colored(f"🔑 Key file exists: {'✅ YES' if key_exists else '❌ NO'}", 
                     "SUCCESS" if key_exists else "ERROR")
        print_colored(f"🔒 Hash file exists: {'✅ YES' if hash_exists else '❌ NO'}", 
                     "SUCCESS" if hash_exists else "ERROR")
        
        if key_exists:
            # Check file is encrypted (not readable as plain text)
            with open(key_file, 'rb') as f:
                content = f.read()
            
            # Should not contain the actual secret in plain text
            contains_secret = b"miketester2390" in content
            print_colored(f"🛡️ Key file encrypted: {'✅ YES' if not contains_secret else '❌ NO'}", 
                         "SUCCESS" if not contains_secret else "ERROR")
        
        print_colored("✅ Secret file system tests completed", "SUCCESS")
        return True
        
    except Exception as e:
        print_colored(f"❌ Secret file test failed: {e}", "ERROR")
        return False

def test_auth_ui():
    """Test authentication UI components"""
    print_colored("\n🖥️ Testing Authentication UI", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    try:
        from auth_ui import auth_ui
        
        print_colored("✅ Auth UI imported successfully", "SUCCESS")
        
        # Test masked input display
        test_secret = "miketester2390"
        masked = auth_ui.auth_manager.get_masked_secret(test_secret)
        expected_pattern = "mi************90"
        
        print_colored(f"🎭 Masked display test: {masked}", "INFO")
        print_colored(f"🎯 Expected pattern: {expected_pattern}", "INFO")
        
        # Check masking works correctly
        is_masked = len(masked) == len(test_secret) and "*" in masked
        print_colored(f"🔍 Masking works: {'✅ YES' if is_masked else '❌ NO'}", 
                     "SUCCESS" if is_masked else "ERROR")
        
        print_colored("✅ Authentication UI tests completed", "SUCCESS")
        return True
        
    except Exception as e:
        print_colored(f"❌ Auth UI test failed: {e}", "ERROR")
        return False

def simulate_auth_flow():
    """Simulate the authentication flow"""
    print_colored("\n🔄 Simulating Authentication Flow", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    try:
        from auth_ui import AuthenticationUI
        
        # Create a test instance
        test_auth = AuthenticationUI()
        
        print_colored("📋 Authentication flow simulation:", "INFO")
        print_colored("1. User runs Model.py", "INFO")
        print_colored("2. System shows authentication header", "INFO")
        
        # Show what the header looks like
        print_colored("\n--- SIMULATED OUTPUT ---", "WARNING")
        test_auth.show_auth_header()
        print_colored("--- END SIMULATION ---\n", "WARNING")
        
        print_colored("3. User enters secret key (hidden input)", "INFO")
        print_colored("4. System validates and shows result", "INFO")
        
        print_colored("✅ Authentication flow simulation completed", "SUCCESS")
        return True
        
    except Exception as e:
        print_colored(f"❌ Auth flow simulation failed: {e}", "ERROR")
        return False

def main():
    """Run all authentication tests"""
    print_colored("🚀 SECURE AUTHENTICATION SYSTEM TESTS", "CYAN", bold=True)
    print_colored("=" * 60, "CYAN", bold=True)
    
    tests = [
        ("Authentication Manager", test_auth_manager),
        ("Secret File System", test_secret_files),
        ("Authentication UI", test_auth_ui),
        ("Authentication Flow", simulate_auth_flow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print_colored(f"❌ {test_name} test crashed: {e}", "ERROR")
    
    print_colored("\n" + "=" * 60, "CYAN", bold=True)
    print_colored(f"📊 TEST RESULTS: {passed}/{total} PASSED", "CYAN", bold=True)
    
    if passed == total:
        print_colored("🎉 ALL TESTS PASSED - AUTHENTICATION SYSTEM READY!", "SUCCESS", bold=True)
        print_colored("🔐 Secret key is securely encrypted and stored", "SUCCESS")
        print_colored("🛡️ Authentication system is production-ready", "SUCCESS")
    else:
        print_colored("⚠️ SOME TESTS FAILED - CHECK ERRORS ABOVE", "ERROR", bold=True)
    
    print_colored("=" * 60, "CYAN", bold=True)

if __name__ == "__main__":
    main()

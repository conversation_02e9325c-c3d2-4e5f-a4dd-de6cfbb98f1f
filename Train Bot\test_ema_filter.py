#!/usr/bin/env python3
"""
Test script to verify EMA filter functionality for PUT signals
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pyquotex.utils.indicators import TechnicalIndicators

def create_test_data():
    """Create test market data for EMA filter testing"""
    # Create sample price data where price moves below and above EMA
    dates = pd.date_range(start='2025-01-01', periods=50, freq='1min')
    
    # Create price data that starts above EMA and then goes below
    base_price = 1.1000
    prices = []
    
    # First 20 candles: trending up (price above EMA)
    for i in range(20):
        price = base_price + (i * 0.0001) + np.random.normal(0, 0.00005)
        prices.append(price)
    
    # Next 15 candles: trending down (price below EMA)
    for i in range(15):
        price = prices[-1] - (i * 0.0002) + np.random.normal(0, 0.00005)
        prices.append(price)
    
    # Last 15 candles: sideways movement
    for i in range(15):
        price = prices[-1] + np.random.normal(0, 0.0001)
        prices.append(price)
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': [p + np.random.normal(0, 0.00002) for p in prices],
        'high': [p + abs(np.random.normal(0, 0.00005)) for p in prices],
        'low': [p - abs(np.random.normal(0, 0.00005)) for p in prices],
        'close': prices,
        'volume': [1000 + np.random.randint(-100, 100) for _ in prices]
    })
    
    return df

def test_ema_filter():
    """Test the EMA filter logic for PUT signals"""
    print("🧪 Testing EMA Filter for PUT Signals")
    print("=" * 50)
    
    # Create test data
    df = create_test_data()
    
    # Calculate 10-period EMA
    close_prices = df['close'].tolist()
    ema_values = TechnicalIndicators.calculate_ema(close_prices, 10)
    
    if not ema_values:
        print("❌ Failed to calculate EMA values")
        return False
    
    current_price = close_prices[-1]
    current_ema = ema_values[-1]
    
    print(f"📊 Current Price: {current_price:.5f}")
    print(f"📈 Current 10-EMA: {current_ema:.5f}")
    print(f"📉 Price vs EMA: {'Below' if current_price < current_ema else 'Above'} EMA")
    
    # Test PUT signal logic
    original_signal = "put"  # Assume strategy generated PUT signal
    
    print(f"\n🎯 Original Signal: {original_signal.upper()}")
    
    # Apply EMA filter
    if original_signal == "put":
        if current_price >= current_ema:
            filtered_signal = "hold"
            print(f"🚫 EMA Filter Applied: PUT signal converted to HOLD (price {current_price:.5f} >= EMA {current_ema:.5f})")
        else:
            filtered_signal = "put"
            print(f"✅ EMA Filter Passed: PUT signal allowed (price {current_price:.5f} < EMA {current_ema:.5f})")
    else:
        filtered_signal = original_signal
        print(f"ℹ️ EMA Filter Skipped: Not a PUT signal")
    
    print(f"🎯 Final Signal: {filtered_signal.upper()}")
    
    # Test with different scenarios
    print(f"\n📋 Testing Different Scenarios:")
    
    # Test last 10 candles
    for i in range(-10, 0):
        price = close_prices[i]
        ema = ema_values[i + len(ema_values) - len(close_prices)]
        
        if "put" == "put":  # Simulate PUT signal
            if price >= ema:
                result = "HOLD (Filtered)"
            else:
                result = "PUT (Allowed)"
        else:
            result = "CALL (No Filter)"
        
        print(f"  Candle {i}: Price={price:.5f}, EMA={ema:.5f} → {result}")
    
    return True

def test_edge_cases():
    """Test edge cases for EMA filter"""
    print(f"\n🔬 Testing Edge Cases")
    print("=" * 30)
    
    # Test with insufficient data
    short_prices = [1.1000, 1.1001, 1.1002]  # Only 3 candles
    ema_short = TechnicalIndicators.calculate_ema(short_prices, 10)
    
    if not ema_short:
        print("✅ Correctly handled insufficient data for EMA calculation")
    else:
        print("❌ Should not calculate EMA with insufficient data")
    
    # Test with exact EMA value
    test_prices = [1.1000] * 15  # Flat prices
    ema_flat = TechnicalIndicators.calculate_ema(test_prices, 10)
    
    if ema_flat:
        current_price = test_prices[-1]
        current_ema = ema_flat[-1]
        print(f"📊 Flat price test: Price={current_price:.5f}, EMA={current_ema:.5f}")
        
        if current_price >= current_ema:
            print("✅ PUT signal would be filtered (price >= EMA)")
        else:
            print("✅ PUT signal would be allowed (price < EMA)")

if __name__ == "__main__":
    try:
        success = test_ema_filter()
        test_edge_cases()
        
        if success:
            print(f"\n🎉 EMA Filter Test Completed Successfully!")
            print("✅ EMA filter is working correctly for PUT signals")
            print("✅ Only PUT signals below 10-EMA will be allowed")
        else:
            print(f"\n❌ EMA Filter Test Failed!")
            
    except Exception as e:
        print(f"❌ Test Error: {e}")
        import traceback
        traceback.print_exc()

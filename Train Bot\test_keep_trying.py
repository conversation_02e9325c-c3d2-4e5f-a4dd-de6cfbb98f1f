#!/usr/bin/env python3
"""
Test script to verify Keep Trying Until Win feature
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_keep_trying_logic():
    """Test the Keep Trying Until Win logic"""
    print("🧪 Testing Keep Trying Until Win Logic")
    print("=" * 50)
    
    # Import the function
    from Model import update_keep_trying_amount
    
    # Test configuration
    keep_trying_enabled = True
    step_amounts = [6.0, 12.0, 24.0, 48.0]  # 4 steps
    base_trade_amount = 3.0
    
    print(f"Base amount: ${base_trade_amount}")
    print(f"Step amounts: {step_amounts}")
    print()
    
    # Test scenarios
    scenarios = [
        # (trade_result, expected_amount, expected_step, expected_losses, description)
        ("loss", 6.0, 1, 1, "First loss -> Step 1"),
        ("loss", 12.0, 2, 2, "Second loss -> Step 2"),
        ("loss", 24.0, 3, 3, "Third loss -> Step 3"),
        ("win", 3.0, 0, 0, "Win -> Reset to base"),
        ("loss", 6.0, 1, 1, "Loss after reset -> Step 1"),
        ("loss", 12.0, 2, 2, "Another loss -> Step 2"),
        ("win", 3.0, 0, 0, "Win -> Reset to base"),
        ("loss", 6.0, 1, 1, "Loss -> Step 1"),
        ("loss", 12.0, 2, 2, "Loss -> Step 2"),
        ("loss", 24.0, 3, 3, "Loss -> Step 3"),
        ("loss", 48.0, 4, 4, "Loss -> Step 4 (final)"),
        ("loss", 3.0, 0, 0, "All steps exhausted -> Reset to base"),
    ]
    
    # Initialize state
    current_step = 0
    consecutive_losses = 0
    current_amount = base_trade_amount
    
    all_passed = True
    
    for i, (trade_result, expected_amount, expected_step, expected_losses, description) in enumerate(scenarios):
        print(f"Test {i+1}: {description}")
        print(f"  Input: result={trade_result}, current_step={current_step}, losses={consecutive_losses}")
        
        # Update amount
        new_amount, new_step, new_losses = update_keep_trying_amount(
            keep_trying_enabled, step_amounts, current_step, consecutive_losses, 
            base_trade_amount, trade_result
        )
        
        # Check results
        if (new_amount == expected_amount and 
            new_step == expected_step and 
            new_losses == expected_losses):
            print(f"  ✅ PASS: amount=${new_amount}, step={new_step}, losses={new_losses}")
        else:
            print(f"  ❌ FAIL: got amount=${new_amount}, step={new_step}, losses={new_losses}")
            print(f"         expected amount=${expected_amount}, step={expected_step}, losses={expected_losses}")
            all_passed = False
        
        # Update state for next test
        current_step = new_step
        consecutive_losses = new_losses
        current_amount = new_amount
        print()
    
    return all_passed

def test_keep_trying_disabled():
    """Test Keep Trying when disabled"""
    print("🧪 Testing Keep Trying When Disabled")
    print("=" * 40)
    
    from Model import update_keep_trying_amount
    
    # Test with disabled feature
    keep_trying_enabled = False
    step_amounts = []
    base_trade_amount = 5.0
    
    # Should always return base amount
    test_results = ["win", "loss", "loss", "win"]
    
    all_passed = True
    for result in test_results:
        new_amount, new_step, new_losses = update_keep_trying_amount(
            keep_trying_enabled, step_amounts, 0, 0, base_trade_amount, result
        )
        
        if new_amount == base_trade_amount and new_step == 0 and new_losses == 0:
            print(f"✅ {result} -> ${new_amount} (correct)")
        else:
            print(f"❌ {result} -> ${new_amount}, step={new_step}, losses={new_losses} (should be ${base_trade_amount}, 0, 0)")
            all_passed = False
    
    return all_passed

def test_keep_trying_edge_cases():
    """Test edge cases for Keep Trying"""
    print(f"\n🧪 Testing Keep Trying Edge Cases")
    print("=" * 40)
    
    from Model import update_keep_trying_amount
    
    # Test with single step
    print("Test: Single step configuration")
    keep_trying_enabled = True
    step_amounts = [10.0]  # Only 1 step
    base_trade_amount = 5.0
    
    # First loss -> step 1
    new_amount, new_step, new_losses = update_keep_trying_amount(
        keep_trying_enabled, step_amounts, 0, 0, base_trade_amount, "loss"
    )
    
    if new_amount == 10.0 and new_step == 1 and new_losses == 1:
        print("✅ First loss -> Step 1 ($10.0)")
    else:
        print(f"❌ First loss -> ${new_amount}, step={new_step}, losses={new_losses}")
        return False
    
    # Second loss -> reset (all steps exhausted)
    new_amount, new_step, new_losses = update_keep_trying_amount(
        keep_trying_enabled, step_amounts, 1, 1, base_trade_amount, "loss"
    )
    
    if new_amount == 5.0 and new_step == 0 and new_losses == 0:
        print("✅ Second loss -> Reset to base ($5.0)")
    else:
        print(f"❌ Second loss -> ${new_amount}, step={new_step}, losses={new_losses}")
        return False
    
    # Test with no result (pending)
    print("\nTest: No result (pending)")
    new_amount, new_step, new_losses = update_keep_trying_amount(
        keep_trying_enabled, [6.0, 12.0], 1, 1, 3.0, "pending"
    )
    
    if new_amount == 6.0 and new_step == 1 and new_losses == 1:
        print("✅ Pending -> Keep current step ($6.0)")
    else:
        print(f"❌ Pending -> ${new_amount}, step={new_step}, losses={new_losses}")
        return False
    
    return True

def main():
    """Run all Keep Trying tests"""
    print("🚀 Testing Keep Trying Until Win Feature")
    print("=" * 60)
    
    results = []
    
    # Test 1: Main logic
    results.append(test_keep_trying_logic())
    
    # Test 2: Disabled feature
    results.append(test_keep_trying_disabled())
    
    # Test 3: Edge cases
    results.append(test_keep_trying_edge_cases())
    
    # Summary
    print(f"\n🎯 Keep Trying Test Results")
    print("=" * 35)
    
    test_names = [
        "Keep Trying Logic",
        "Disabled Feature",
        "Edge Cases"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Keep Trying Until Win feature working correctly!")
        print("\n📋 Feature Summary:")
        print("   ✅ Automatically increases trade amount after losses")
        print("   ✅ Resets to base amount after a win")
        print("   ✅ Handles step progression correctly")
        print("   ✅ Resets after all steps exhausted")
        print("   ✅ Works correctly when disabled")
        print("   ✅ Handles edge cases properly")
    else:
        print("⚠️ Some issues need attention")
    
    return passed == total

if __name__ == "__main__":
    main()

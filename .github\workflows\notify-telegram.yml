# .github/workflows/notify-telegram.yml

name: Notify Telegram

on:
  push:
    branches:
      - master  # Not<PERSON><PERSON> commits ou pushes na branch "master"
  pull_request:
    types: [opened, closed]

jobs:
  notify-telegram:
    runs-on: ubuntu-24.04

    steps:
      - name: Checkout do reposi<PERSON><PERSON><PERSON>
        uses: actions/checkout@v3

      - name: Enviar notificação para o Telegram
        env:
          TELEGRAM_TOKEN: ${{ secrets.TELEGRAM_TOKEN }}
          TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
        run: |
          escape_markdown() {
            local input="$1"
            echo "$input" | sed 's/[][\.*^$(){}?+|&]/\\&/g'
          }

          if [ "${{ github.event_name }}" = "push" ]; then
            COMMIT_MESSAGE=$(git log -1 --pretty=%B)
            CLEAN_MESSAGE=$(echo "$COMMIT_MESSAGE" | sed 's/:[a-z_]*://g')
            ESCAPED_MESSAGE=$(escape_markdown "$CLEAN_MESSAGE")
            MESSAGE="📦 *New Push on Repository*\n\n🔧 Repository: ${{ github.repository }}\n👤 Actor: ${{ github.actor }}\n📝 Message Commit:\n${ESCAPED_MESSAGE}"
          elif [ "${{ github.event_name }}" = "pull_request" ]; then
            ACTION="${{ github.event.action }}"
            PR_URL="${{ github.event.pull_request.html_url }}"
            PR_TITLE="${{ github.event.pull_request.title }}"
            MESSAGE="📌 *Pull Request ${ACTION}:*\n\n🔧 Repository: ${{ github.repository }}\n👤 Actor: ${{ github.actor }}\n📝 Title: ${PR_TITLE}\n🔗 [View PR](${PR_URL})"
          fi

          curl -X POST "https://api.telegram.org/bot$TELEGRAM_TOKEN/sendMessage" \
            -H "Content-Type: application/json" \
            -d "{\"chat_id\": \"$TELEGRAM_CHAT_ID\", \"text\": \"$MESSAGE\", \"parse_mode\": \"Markdown\"}"

# Quotex Trading Bot Requirements
# Install with: pip install -r requirements_quotex.txt

# Core dependencies
# Note: quotexpy library is included in the project folder
pandas>=1.3.0
numpy>=1.21.0
requests>=2.25.0

# HTTP requests and API handling
aiohttp>=3.8.0

# Optional: For advanced technical analysis
ta-lib>=0.4.0  # Technical Analysis Library (optional)
plotly>=5.0.0  # For plotting (optional)

# Development and testing (optional)
pytest>=6.0.0
black>=21.0.0
flake8>=3.9.0

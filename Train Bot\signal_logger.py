#!/usr/bin/env python3
"""
Signal Logger Module for Enhanced Trading Bot
Handles real-time signal logging, result tracking, and performance summary
"""

import json
import os
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any
from utils import print_colored

# Global variables for signal tracking
active_signals: List[Dict[str, Any]] = []
performance_summary: Dict[str, Dict[str, int]] = {}
daily_signals: List[Dict[str, Any]] = []

def initialize_signal_logger():
    """Initialize the signal logger and load today's signals if they exist"""
    global daily_signals, performance_summary
    
    # Create signals directory if it doesn't exist
    signals_dir = "signals"
    if not os.path.exists(signals_dir):
        os.makedirs(signals_dir)
        print_colored("📁 Created signals directory", "SUCCESS")
    
    # Load today's signals if file exists
    today = datetime.now().strftime("%Y-%m-%d")
    signals_file = os.path.join(signals_dir, f"signals_{today}.json")
    
    if os.path.exists(signals_file):
        try:
            with open(signals_file, 'r') as f:
                content = f.read().strip()
                if content:  # Check if file is not empty
                    daily_signals = json.loads(content)
                else:
                    daily_signals = []

            # Rebuild performance summary from loaded signals
            performance_summary = {}
            for signal in daily_signals:
                if signal.get('result') in ['win', 'loss']:
                    pair = signal['pair']
                    if pair not in performance_summary:
                        performance_summary[pair] = {'total': 0, 'wins': 0, 'losses': 0}

                    performance_summary[pair]['total'] += 1
                    if signal['result'] == 'win':
                        performance_summary[pair]['wins'] += 1
                    else:
                        performance_summary[pair]['losses'] += 1

            if daily_signals:
                print_colored(f"📊 Loaded {len(daily_signals)} signals from {signals_file}", "SUCCESS")

        except Exception as e:
            print_colored(f"⚠️ Error loading signals file: {e}", "WARNING")
            daily_signals = []
            # Create empty file to prevent future errors
            with open(signals_file, 'w') as f:
                json.dump([], f)
    else:
        daily_signals = []
        # Create empty signals file
        with open(signals_file, 'w') as f:
            json.dump([], f)

def save_signal(signal_data: Dict[str, Any]) -> bool:
    """
    Save signal to memory and daily file
    
    Args:
        signal_data: Dictionary containing signal information
        
    Returns:
        bool: True if saved successfully, False otherwise
    """
    global active_signals, daily_signals
    
    try:
        # Add to active signals (for pending evaluation)
        if signal_data.get('result') == 'pending':
            active_signals.append(signal_data.copy())
        
        # Add to daily signals
        daily_signals.append(signal_data.copy())
        
        # Save to daily file
        today = datetime.now().strftime("%Y-%m-%d")
        signals_dir = "signals"
        signals_file = os.path.join(signals_dir, f"signals_{today}.json")
        
        with open(signals_file, 'w') as f:
            json.dump(daily_signals, f, indent=2, default=str)
        
        # Signal saved silently - no verbose output
        return True

    except Exception as e:
        print_colored(f"❌ Error saving signal: {e}", "ERROR")
        return False

def evaluate_last_signal(pair: str, new_price: float) -> Optional[Dict[str, Any]]:
    """
    Evaluate the last pending signal for a pair against new price
    
    Args:
        pair: Trading pair name
        new_price: Current market price
        
    Returns:
        Dict with evaluation result or None if no pending signal
    """
    global active_signals, performance_summary, daily_signals
    
    try:
        # Find the most recent pending signal for this pair
        pending_signal = None
        signal_index = None
        
        for i, signal in enumerate(active_signals):
            if signal['pair'] == pair and signal.get('result') == 'pending':
                pending_signal = signal
                signal_index = i
                break
        
        if not pending_signal:
            return None
        
        # Evaluate the signal
        signal_price = pending_signal['price']
        direction = pending_signal['direction']
        
        # Determine win/loss
        if direction == "call":
            result = "win" if new_price > signal_price else "loss"
        else:  # direction == "put"
            result = "win" if new_price < signal_price else "loss"
        
        # Update signal with result
        evaluation_time = datetime.now().strftime("%H:%M:%S")
        pending_signal.update({
            'result': result,
            'final_price': new_price,
            'evaluation_time': evaluation_time
        })
        
        # Remove from active signals
        active_signals.pop(signal_index)
        
        # Update daily signals file
        for daily_signal in daily_signals:
            if (daily_signal['pair'] == pair and 
                daily_signal['timestamp'] == pending_signal['timestamp'] and
                daily_signal.get('result') == 'pending'):
                daily_signal.update({
                    'result': result,
                    'final_price': new_price,
                    'evaluation_time': evaluation_time
                })
                break
        
        # Save updated daily signals
        today = datetime.now().strftime("%Y-%m-%d")
        signals_dir = "signals"
        signals_file = os.path.join(signals_dir, f"signals_{today}.json")
        
        with open(signals_file, 'w') as f:
            json.dump(daily_signals, f, indent=2, default=str)
        
        # Update performance summary
        update_summary(pair, result)
        
        # Print result
        result_color = "SUCCESS" if result == "win" else "ERROR"
        print_colored(f"✅ Next Candle Result at {evaluation_time}", "INFO", bold=True)
        print_colored(f"Pair: {pair} | New Price: {new_price:.5f} → RESULT: {result.upper()}", result_color)
        print()
        
        return {
            'pair': pair,
            'result': result,
            'signal_price': signal_price,
            'final_price': new_price,
            'direction': direction,
            'evaluation_time': evaluation_time
        }
        
    except Exception as e:
        print_colored(f"❌ Error evaluating signal for {pair}: {e}", "ERROR")
        return None

def update_summary(pair: str, result: str):
    """
    Update performance summary for a pair
    
    Args:
        pair: Trading pair name
        result: 'win' or 'loss'
    """
    global performance_summary
    
    if pair not in performance_summary:
        performance_summary[pair] = {'total': 0, 'wins': 0, 'losses': 0}
    
    performance_summary[pair]['total'] += 1
    if result == 'win':
        performance_summary[pair]['wins'] += 1
    else:
        performance_summary[pair]['losses'] += 1

def print_summary_box(selected_pairs: List[str]):
    """
    Print the performance summary box
    
    Args:
        selected_pairs: List of selected trading pairs
    """
    global performance_summary
    
    print_colored("=" * 60, "SKY_BLUE")
    print_colored("📊 Signal Summary", "OCEAN", bold=True)
    print_colored(f"Total Pairs Selected: {len(selected_pairs)} ({', '.join(selected_pairs[:2])}{'...' if len(selected_pairs) > 2 else ''})", "INFO")
    print()
    
    # Table header
    print_colored("Pairs           | Total Signals | Wins | Losses", "OCEAN", bold=True)
    print_colored("-" * 48, "SKY_BLUE")
    
    # Calculate totals
    total_signals = 0
    total_wins = 0
    total_losses = 0
    
    # Display each pair's performance
    for pair in selected_pairs:
        if pair in performance_summary:
            stats = performance_summary[pair]
            total = stats['total']
            wins = stats['wins']
            losses = stats['losses']
        else:
            total = wins = losses = 0
        
        # Format pair name (truncate if too long)
        pair_display = pair[:15] if len(pair) <= 15 else pair[:12] + "..."
        
        print_colored(f"{pair_display:<15} |      {total:02d}       |  {wins:02d}  |   {losses:02d}", "ROSEWOOD")
        
        total_signals += total
        total_wins += wins
        total_losses += losses
    
    # Overall summary
    print_colored("-" * 48, "SKY_BLUE")
    print_colored(f"{'Overall':<15} |      {total_signals:02d}       |  {total_wins:02d}  |   {total_losses:02d}", "SUCCESS", bold=True)
    print_colored("=" * 60, "SKY_BLUE")
    print()

def get_current_indicators(df) -> Dict[str, float]:
    """
    Extract current indicator values from DataFrame
    
    Args:
        df: DataFrame with technical indicators
        
    Returns:
        Dictionary of current indicator values
    """
    try:
        if df is None or len(df) == 0:
            return {}
        
        latest = df.iloc[-1]
        
        indicators = {}
        
        # RSI
        if 'rsi' in df.columns:
            indicators['rsi'] = round(float(latest['rsi']), 2) if not pd.isna(latest['rsi']) else None
        
        # EMAs
        if 'ema_12' in df.columns:
            indicators['ema_12'] = round(float(latest['ema_12']), 5) if not pd.isna(latest['ema_12']) else None
        if 'ema_26' in df.columns:
            indicators['ema_26'] = round(float(latest['ema_26']), 5) if not pd.isna(latest['ema_26']) else None
        
        # MACD
        if 'macd' in df.columns:
            indicators['macd'] = round(float(latest['macd']), 6) if not pd.isna(latest['macd']) else None
        if 'macd_signal' in df.columns:
            indicators['macd_signal'] = round(float(latest['macd_signal']), 6) if not pd.isna(latest['macd_signal']) else None
        
        # SMAs
        if 'sma_20' in df.columns:
            indicators['sma_20'] = round(float(latest['sma_20']), 5) if not pd.isna(latest['sma_20']) else None
        
        # Bollinger Bands
        if 'bb_upper' in df.columns:
            indicators['bb_upper'] = round(float(latest['bb_upper']), 5) if not pd.isna(latest['bb_upper']) else None
        if 'bb_lower' in df.columns:
            indicators['bb_lower'] = round(float(latest['bb_lower']), 5) if not pd.isna(latest['bb_lower']) else None
        
        return indicators
        
    except Exception as e:
        print_colored(f"⚠️ Error extracting indicators: {e}", "WARNING")
        return {}

def clear_old_signals():
    """Clear active signals older than 2 minutes (safety cleanup)"""
    global active_signals
    
    try:
        current_time = datetime.now()
        cleaned_signals = []
        
        for signal in active_signals:
            signal_time = datetime.strptime(f"{signal['date']} {signal['timestamp']}", "%Y-%m-%d %H:%M:%S")
            time_diff = (current_time - signal_time).total_seconds()
            
            # Keep signals less than 2 minutes old
            if time_diff < 120:
                cleaned_signals.append(signal)
        
        removed_count = len(active_signals) - len(cleaned_signals)
        if removed_count > 0:
            print_colored(f"🧹 Cleaned {removed_count} old pending signals", "INFO")
        
        active_signals = cleaned_signals
        
    except Exception as e:
        print_colored(f"⚠️ Error cleaning old signals: {e}", "WARNING")

def print_echo_sniper_summary(selected_pairs: List[str]):
    """
    Print Echo Sniper specific summary with pattern information

    Args:
        selected_pairs: List of selected trading pairs
    """
    global daily_signals

    print_colored("=" * 80, "SKY_BLUE")
    print_colored("🎯 ECHO SNIPER STRATEGY SUMMARY", "OCEAN", bold=True)
    print_colored(f"Total Pairs Monitored: {len(selected_pairs)}", "INFO")
    print()

    # Analyze Echo Sniper signals
    echo_signals = [s for s in daily_signals if s.get('strategy_used') == 'ECHO_SNIPER']

    if not echo_signals:
        print_colored("📊 No Echo Sniper signals generated yet", "INFO")
        print_colored("=" * 80, "SKY_BLUE")
        return

    # Pattern analysis
    patterns_used = {}
    total_signals = len(echo_signals)
    wins = sum(1 for s in echo_signals if s.get('result') == 'win')
    losses = sum(1 for s in echo_signals if s.get('result') == 'loss')
    pending = sum(1 for s in echo_signals if s.get('result') == 'pending')

    # Collect pattern statistics
    for signal in echo_signals:
        pattern = signal.get('pattern_used')
        if pattern:
            pattern_str = ' → '.join(pattern)
            if pattern_str not in patterns_used:
                patterns_used[pattern_str] = {'count': 0, 'wins': 0, 'losses': 0}
            patterns_used[pattern_str]['count'] += 1
            if signal.get('result') == 'win':
                patterns_used[pattern_str]['wins'] += 1
            elif signal.get('result') == 'loss':
                patterns_used[pattern_str]['losses'] += 1

    # Display overall statistics
    print_colored("📈 OVERALL PERFORMANCE:", "SUCCESS", bold=True)
    print_colored(f"   Total Signals: {total_signals}", "INFO")
    print_colored(f"   Wins: {wins} | Losses: {losses} | Pending: {pending}", "INFO")
    if wins + losses > 0:
        win_rate = wins / (wins + losses)
        print_colored(f"   Win Rate: {win_rate:.1%}", "SUCCESS" if win_rate >= 0.6 else "WARNING")
    print()

    # Display pattern analysis
    if patterns_used:
        print_colored("🔍 PATTERN ANALYSIS:", "SUCCESS", bold=True)
        print_colored("Pattern                    | Used | Wins | Losses | Win Rate", "OCEAN", bold=True)
        print_colored("-" * 65, "SKY_BLUE")

        for pattern, stats in patterns_used.items():
            pattern_display = pattern[:25] if len(pattern) <= 25 else pattern[:22] + "..."
            total = stats['wins'] + stats['losses']
            win_rate = stats['wins'] / total if total > 0 else 0
            win_rate_color = "SUCCESS" if win_rate >= 0.6 else "WARNING" if win_rate >= 0.4 else "ERROR"

            print_colored(f"{pattern_display:<25} | {stats['count']:4d} | {stats['wins']:4d} | {stats['losses']:6d} | ", "ROSEWOOD", end="")
            print_colored(f"{win_rate:.1%}", win_rate_color)

    print_colored("=" * 80, "SKY_BLUE")
    print()

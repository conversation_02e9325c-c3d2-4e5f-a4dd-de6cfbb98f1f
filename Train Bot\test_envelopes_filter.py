#!/usr/bin/env python3
"""
Test script to verify Envelopes indicator filter implementation
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_envelopes_calculation():
    """Test Envelopes indicator calculation"""
    print("🧪 Testing Envelopes Indicator Calculation")
    print("=" * 50)
    
    # Import the function
    from Model import calculate_envelopes
    
    # Test data: Sample close prices
    test_prices = [
        100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0,
        110.0, 111.0, 112.0, 113.0, 114.0, 115.0, 116.0, 117.0, 118.0, 119.0
    ]
    
    print(f"Test data: {len(test_prices)} price points")
    print(f"Price range: {min(test_prices)} - {max(test_prices)}")
    print()
    
    # Test with default settings: period=14, deviation=0.03
    sma, top_envelope, bottom_envelope = calculate_envelopes(test_prices, period=14, deviation=0.03, ma_type='sma')
    
    if sma is not None and top_envelope is not None and bottom_envelope is not None:
        print("✅ Envelopes calculation successful")
        print(f"   SMA (14): {sma:.5f}")
        print(f"   Top Envelope: {top_envelope:.5f}")
        print(f"   Bottom Envelope: {bottom_envelope:.5f}")
        print(f"   Deviation: {((top_envelope - sma) / sma * 100):.2f}%")
        
        # Verify calculation
        expected_top = sma + (sma * 0.03)
        expected_bottom = sma - (sma * 0.03)
        
        if abs(top_envelope - expected_top) < 0.0001 and abs(bottom_envelope - expected_bottom) < 0.0001:
            print("✅ Calculation accuracy verified")
            return True
        else:
            print("❌ Calculation accuracy failed")
            return False
    else:
        print("❌ Envelopes calculation failed")
        return False

def test_envelopes_filter_logic():
    """Test Envelopes filter logic for PUT signals"""
    print(f"\n🧪 Testing Envelopes Filter Logic")
    print("=" * 40)
    
    from Model import calculate_envelopes
    
    # Test scenario 1: Price below top envelope (should allow PUT)
    print("Scenario 1: Price below top envelope")
    test_prices = [100.0] * 14 + [99.0]  # 15 prices, last one below average
    current_price = 99.0
    
    sma, top_envelope, bottom_envelope = calculate_envelopes(test_prices, period=14, deviation=0.03, ma_type='sma')
    
    if top_envelope is not None:
        print(f"   Current price: {current_price}")
        print(f"   Top envelope: {top_envelope:.5f}")
        
        if current_price < top_envelope:
            print("   ✅ PUT signal should be ALLOWED (price below top envelope)")
            result1 = True
        else:
            print("   ❌ PUT signal should be allowed but logic failed")
            result1 = False
    else:
        print("   ❌ Calculation failed")
        result1 = False
    
    print()
    
    # Test scenario 2: Price above top envelope (should block PUT)
    print("Scenario 2: Price above top envelope")
    test_prices = [100.0] * 14 + [105.0]  # 15 prices, last one above average
    current_price = 105.0
    
    sma, top_envelope, bottom_envelope = calculate_envelopes(test_prices, period=14, deviation=0.03, ma_type='sma')
    
    if top_envelope is not None:
        print(f"   Current price: {current_price}")
        print(f"   Top envelope: {top_envelope:.5f}")
        
        if current_price >= top_envelope:
            print("   ✅ PUT signal should be BLOCKED (price above top envelope)")
            result2 = True
        else:
            print("   ❌ PUT signal should be blocked but logic failed")
            result2 = False
    else:
        print("   ❌ Calculation failed")
        result2 = False
    
    return result1 and result2

def test_envelopes_vs_ema():
    """Compare Envelopes vs EMA approach"""
    print(f"\n🧪 Testing Envelopes vs EMA Comparison")
    print("=" * 45)
    
    from Model import calculate_envelopes
    
    # Test with trending data
    trending_up_prices = [100 + i for i in range(20)]  # Uptrend: 100, 101, 102, ..., 119
    current_price = 119.0
    
    print("Uptrend scenario:")
    print(f"   Prices: {trending_up_prices[:5]}...{trending_up_prices[-5:]}")
    print(f"   Current price: {current_price}")
    
    # Calculate Envelopes
    sma, top_envelope, bottom_envelope = calculate_envelopes(trending_up_prices, period=14, deviation=0.03, ma_type='sma')
    
    if top_envelope is not None:
        print(f"   SMA (14): {sma:.2f}")
        print(f"   Top envelope: {top_envelope:.2f}")
        
        if current_price >= top_envelope:
            print("   ✅ Envelopes filter: PUT signal BLOCKED (uptrend detected)")
            envelope_result = True
        else:
            print("   ⚠️ Envelopes filter: PUT signal allowed (may miss uptrend)")
            envelope_result = False
    else:
        print("   ❌ Envelopes calculation failed")
        envelope_result = False
    
    print()
    
    # Test with sideways data
    sideways_prices = [100 + (i % 3 - 1) for i in range(20)]  # Sideways: 99, 100, 101, 99, 100, 101...
    current_price = 99.0
    
    print("Sideways scenario:")
    print(f"   Prices: {sideways_prices[:10]}...")
    print(f"   Current price: {current_price}")
    
    # Calculate Envelopes
    sma, top_envelope, bottom_envelope = calculate_envelopes(sideways_prices, period=14, deviation=0.03, ma_type='sma')
    
    if top_envelope is not None:
        print(f"   SMA (14): {sma:.2f}")
        print(f"   Top envelope: {top_envelope:.2f}")
        
        if current_price < top_envelope:
            print("   ✅ Envelopes filter: PUT signal ALLOWED (no strong uptrend)")
            sideways_result = True
        else:
            print("   ⚠️ Envelopes filter: PUT signal blocked (may be too restrictive)")
            sideways_result = False
    else:
        print("   ❌ Envelopes calculation failed")
        sideways_result = False
    
    return envelope_result and sideways_result

def main():
    """Run all Envelopes filter tests"""
    print("🚀 Testing Envelopes Indicator Filter")
    print("=" * 60)
    
    results = []
    
    # Test 1: Calculation accuracy
    results.append(test_envelopes_calculation())
    
    # Test 2: Filter logic
    results.append(test_envelopes_filter_logic())
    
    # Test 3: Comparison with EMA
    results.append(test_envelopes_vs_ema())
    
    # Summary
    print(f"\n🎯 Envelopes Filter Test Results")
    print("=" * 40)
    
    test_names = [
        "Envelopes Calculation",
        "Filter Logic",
        "Trend Detection"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Envelopes filter working correctly!")
        print("\n📋 Filter Summary:")
        print("   ✅ Envelopes indicator calculated accurately")
        print("   ✅ PUT signals blocked when price above top envelope")
        print("   ✅ PUT signals allowed when price below top envelope")
        print("   ✅ Uptrend detection working properly")
        print("\n🔧 Settings:")
        print("   • Period: 14")
        print("   • Deviation: 0.03 (3%)")
        print("   • Moving Average: SMA")
        print("   • Applied to: Close price")
        print("\n🎯 Logic:")
        print("   • Price >= Top Envelope → Block PUT signals (uptrend)")
        print("   • Price < Top Envelope → Allow PUT signals")
    else:
        print("⚠️ Some issues need attention")
    
    return passed == total

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Debug script to check candle data from WebSocket
"""

import asyncio
import sys
import os

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client

async def debug_candle_data():
    """Debug candle data retrieval"""
    print("🔍 Debugging Candle Data Retrieval")
    print("=" * 50)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Create client instance
        print("🔗 Creating Quotex client...")
        client = get_quotex_client(email, password, demo_mode=True)
        
        # Connect with WebSocket
        print("🔐 Connecting to Quotex...")
        connected = await client.connect()
        
        if not connected:
            print("❌ Failed to connect")
            return
        
        print("✅ Connected successfully!")
        
        # Wait for tick data to accumulate
        print("⏳ Waiting 15 seconds for tick data...")
        await asyncio.sleep(15)
        
        # Test asset
        test_asset = "EURUSD_otc"
        
        # Check tick data
        if test_asset in client.tick_data:
            tick_count = len(client.tick_data[test_asset])
            print(f"📊 Tick data count: {tick_count}")
            
            if tick_count > 0:
                latest_tick = client.tick_data[test_asset][-1]
                print(f"📈 Latest tick: {latest_tick}")
                
                # Check current price
                current_price = client.current_prices.get(test_asset, 0)
                print(f"💰 Current price: {current_price}")
        else:
            print(f"❌ No tick data for {test_asset}")
        
        # Test candle building
        print(f"\n🕯️ Testing candle building for {test_asset}...")
        candles = await client.get_candles_browser(test_asset, period=60, count=10)
        
        if candles:
            print(f"✅ Retrieved {len(candles)} candles")
            for i, candle in enumerate(candles):
                print(f"  Candle {i+1}: O:{candle['open']:.5f} H:{candle['high']:.5f} L:{candle['low']:.5f} C:{candle['close']:.5f} V:{candle['volume']}")
        else:
            print("❌ No candles retrieved")
        
        print("\n✅ Debug completed!")
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            try:
                await client.close()
            except:
                pass

if __name__ == "__main__":
    asyncio.run(debug_candle_data())

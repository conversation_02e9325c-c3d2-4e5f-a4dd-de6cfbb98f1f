#!/usr/bin/env python3
"""
Navigate to Trading Area and Find Trade Buttons
"""

import asyncio
import sys
import os
import time

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client

async def navigate_to_trading():
    """Navigate to trading area and find trade buttons"""
    print("🎯 NAVIGATING TO TRADING AREA")
    print("=" * 60)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Connect
        print("🔗 Connecting...")
        client = get_quotex_client(email, password, demo_mode=True)
        connected = await client.connect()
        
        if not connected:
            print("❌ Connection failed")
            return
        
        print("✅ Connected successfully")
        
        # Navigate to trading page
        print("\n🎯 Navigating to trading page...")
        try:
            await client.page.goto("https://market-qx.pro/pt/trade", timeout=30000)
            await asyncio.sleep(5)  # Wait for page to load
            print("✅ Navigated to trading page")
        except Exception as e:
            print(f"⚠️ Navigation error: {e}")
            print("Continuing with current page...")
        
        # Take screenshot of trading page
        print("\n📸 Taking trading page screenshot...")
        await client.page.screenshot(path="trading_page.png", full_page=True)
        print("✅ Screenshot saved: trading_page.png")
        
        # Look for trading interface elements
        print("\n🔍 Searching for trading interface...")
        
        # Common trading interface selectors
        trading_selectors = [
            '.trading-panel', '.trade-panel', '.option-panel',
            '.binary-options', '.trading-interface', '.trade-interface',
            '.trading-area', '.trade-area', '.options-trading'
        ]
        
        for selector in trading_selectors:
            try:
                elements = await client.page.query_selector_all(selector)
                if elements:
                    print(f"  ✅ Found trading interface: {selector} ({len(elements)} elements)")
            except:
                continue
        
        # Look for specific trade buttons with more comprehensive search
        print("\n🔍 Comprehensive trade button search...")
        
        # Try different button selectors
        button_selectors = [
            # Text-based
            'button:has-text("UP")', 'button:has-text("DOWN")',
            'button:has-text("CALL")', 'button:has-text("PUT")',
            'button:has-text("HIGHER")', 'button:has-text("LOWER")',
            'button:has-text("ACIMA")', 'button:has-text("ABAIXO")',
            'button:has-text("COMPRAR")', 'button:has-text("VENDER")',
            
            # Class-based
            '.call-btn', '.put-btn', '.up-btn', '.down-btn',
            '.higher-btn', '.lower-btn', '.buy-btn', '.sell-btn',
            '.trade-up', '.trade-down', '.option-up', '.option-down',
            
            # Data attribute based
            '[data-direction="up"]', '[data-direction="down"]',
            '[data-direction="call"]', '[data-direction="put"]',
            '[data-action="buy"]', '[data-action="sell"]',
            
            # Generic trading buttons
            '.trading-button', '.trade-button', '.option-button',
            '.binary-button', '.investment-button'
        ]
        
        found_buttons = []
        for selector in button_selectors:
            try:
                elements = await client.page.query_selector_all(selector)
                if elements:
                    print(f"  ✅ Found: {selector} ({len(elements)} elements)")
                    found_buttons.append((selector, len(elements)))
            except:
                continue
        
        # If no specific buttons found, look for any clickable elements with trade text
        if not found_buttons:
            print("\n🔍 Looking for any clickable elements with trade text...")
            
            clickable_selectors = [
                'div:has-text("UP")', 'div:has-text("DOWN")',
                'span:has-text("CALL")', 'span:has-text("PUT")',
                'a:has-text("HIGHER")', 'a:has-text("LOWER")',
                '[role="button"]:has-text("UP")', '[role="button"]:has-text("DOWN")'
            ]
            
            for selector in clickable_selectors:
                try:
                    elements = await client.page.query_selector_all(selector)
                    if elements:
                        print(f"  ✅ Found clickable: {selector} ({len(elements)} elements)")
                        found_buttons.append((selector, len(elements)))
                except:
                    continue
        
        # Try to find buttons by color (green for UP/CALL, red for DOWN/PUT)
        print("\n🔍 Looking for buttons by color...")
        color_selectors = [
            'button[style*="green"]', 'button[style*="red"]',
            '.green-button', '.red-button',
            '.btn-success', '.btn-danger',
            '.positive-button', '.negative-button'
        ]
        
        for selector in color_selectors:
            try:
                elements = await client.page.query_selector_all(selector)
                if elements:
                    print(f"  ✅ Found colored button: {selector} ({len(elements)} elements)")
                    found_buttons.append((selector, len(elements)))
            except:
                continue
        
        # Get all buttons and analyze their content
        print("\n🔍 Analyzing all buttons on page...")
        all_buttons = await client.page.query_selector_all('button')
        print(f"📊 Total buttons found: {len(all_buttons)}")
        
        trade_related_buttons = []
        for i, button in enumerate(all_buttons):
            try:
                text = await button.text_content()
                is_visible = await button.is_visible()
                
                if text and is_visible:
                    text_upper = text.strip().upper()
                    trade_keywords = ['UP', 'DOWN', 'CALL', 'PUT', 'HIGHER', 'LOWER', 
                                    'ACIMA', 'ABAIXO', 'COMPRAR', 'VENDER', 'BUY', 'SELL']
                    
                    if any(keyword in text_upper for keyword in trade_keywords):
                        trade_related_buttons.append({
                            'index': i,
                            'text': text.strip(),
                            'selector': f'button:nth-child({i+1})'
                        })
            except:
                continue
        
        print(f"\n🎯 TRADE-RELATED BUTTONS FOUND: {len(trade_related_buttons)}")
        for btn in trade_related_buttons:
            print(f"  Button {btn['index']}: '{btn['text']}' -> {btn['selector']}")
        
        # Summary and recommendations
        print(f"\n📋 SUMMARY:")
        print(f"✅ Trading page screenshot: trading_page.png")
        print(f"📊 Total buttons: {len(all_buttons)}")
        print(f"🎯 Trade buttons found: {len(trade_related_buttons)}")
        print(f"🔍 Working selectors: {len(found_buttons)}")
        
        if trade_related_buttons:
            print(f"\n🎯 RECOMMENDED INSTANT SELECTORS:")
            for btn in trade_related_buttons:
                print(f"  '{btn['text']}' -> button:has-text(\"{btn['text']}\")")
        
        if found_buttons:
            print(f"\n✅ WORKING SELECTORS:")
            for selector, count in found_buttons:
                print(f"  {selector} ({count} elements)")
        
        if not trade_related_buttons and not found_buttons:
            print(f"\n⚠️ NO TRADE BUTTONS FOUND")
            print(f"📸 Check trading_page.png for manual analysis")
            print(f"🔧 May need to navigate to a different trading section")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            try:
                await client.close()
                print("🔌 Connection closed")
            except:
                pass

if __name__ == "__main__":
    asyncio.run(navigate_to_trading())

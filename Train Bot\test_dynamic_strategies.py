#!/usr/bin/env python3
"""
Test dynamic strategy engine with different candle counts
"""

import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from Model import fetch_quotex_market_data
from strategy_engine import StrategyEngine
from utils import print_colored

async def test_dynamic_strategies():
    """Test strategies with different candle counts"""
    print_colored("🧪 Testing Dynamic Strategy Engine", "HEADER", bold=True)
    print_colored("=" * 60, "HEADER")
    
    strategy_engine = StrategyEngine()
    test_pair = "USDJPY"  # Use reliable Live pair
    candle_counts = [10, 15, 20, 30, 50, 100]
    
    for candle_count in candle_counts:
        print_colored(f"\n🔍 Testing {test_pair} with {candle_count} candles...", "INFO")
        
        try:
            # Get real data
            df = await fetch_quotex_market_data(test_pair, "M1", candle_count)
            
            if df is not None and len(df) > 0:
                print_colored(f"✅ Retrieved {len(df)} real candles", "SUCCESS")
                
                # Test Strategy 1
                signal1, conf1 = strategy_engine.evaluate_strategy_1(df)
                signal1_text = "CALL" if signal1 == 1 else ("PUT" if signal1 == -1 else "HOLD")
                
                # Test Strategy 4
                signal4, conf4 = strategy_engine.evaluate_strategy_4(df)
                signal4_text = "CALL" if signal4 == 1 else ("PUT" if signal4 == -1 else "HOLD")
                
                print_colored(f"📊 Strategy 1: {signal1_text} ({conf1:.1%})", "INFO")
                print_colored(f"📊 Strategy 4: {signal4_text} ({conf4:.1%})", "INFO")
                
                # Show price info
                latest_price = df['close'].iloc[-1]
                price_range = f"{df['low'].min():.5f} - {df['high'].max():.5f}"
                print_colored(f"💰 Price: {latest_price:.5f} (Range: {price_range})", "INFO")
                
            else:
                print_colored(f"❌ No real data available for {candle_count} candles", "ERROR")
                
        except Exception as e:
            print_colored(f"❌ Error with {candle_count} candles: {str(e)}", "ERROR")
    
    print_colored("\n✅ Dynamic strategy testing completed!", "SUCCESS")

if __name__ == "__main__":
    asyncio.run(test_dynamic_strategies())

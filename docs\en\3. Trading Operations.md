# Trading Operations Documentation - PyQuotex

## 1. Simple Buy Operation

The simple buy operation allows performing a basic transaction by specifying the amount, asset, direction, and duration.

```python
async def buy_simple():
    amount = 50  # Amount in account currency
    asset = "AUDCAD"  # Asset code
    direction = "call"  # Direction: "call" (up) or "put" (down)
    duration = 60  # Duration in seconds
    
    status, buy_info = await client.buy(amount, asset_name, direction, duration)
    # status: True/False indicating if operation was successful
    # buy_info: Detailed order information
```

## 2. Buy with Result Verification

This operation allows making a purchase and automatically waiting for the result:

```python
async def buy_and_check_win():
    amount = 50
    asset = "EURUSD_otc"
    direction = "call"
    duration = 60
    
    status, buy_info = await client.buy(amount, asset_name, direction, duration)
    if status:
        # Wait for result
        if await client.check_win(buy_info["id"]):
            profit = client.get_profit()
            print(f"Win! Profit: {profit}")
        else:
            loss = client.get_profit()
            print(f"Loss: {loss}")
```

## 3. Multiple Buys

Allows performing multiple sequential operations:

```python
order_list = [
    {"amount": 5, "asset": "EURUSD", "direction": "call", "duration": 60},
    {"amount": 10, "asset": "AUDCAD_otc", "direction": "put", "duration": 60},
    # ... more orders
]

async def buy_multiple(orders=10):
    for i in range(orders):
        order = random.choice(order_list)
        status, buy_info = await client.buy(**order)
```

## 4. Pending Orders

Pending orders allow scheduling operations to execute at a specific time:

```python
async def buy_pending():
    amount = 50
    asset = "AUDCAD"
    direction = "call"
    duration = 60
    open_time = "16/12 15:51"  # Format: "dd/mm HH:MM"
    
    status, buy_info = await client.open_pending(
        amount, 
        asset_name, 
        direction, 
        duration, 
        open_time
    )
```

## 5. Selling Options

Allows closing a position before expiration:

```python
async def sell_option():
    # First open a position
    status, buy_info = await client.buy(amount, asset_name, direction, duration)
    
    # Then sell the option using its ID
    result = await client.sell_option(buy_info["id"])
```

## 6. Result Verification

There are two ways to verify results:

### 6.1 Direct Verification
```python
async def check_result(operation_id):
    status, operation_info = await client.get_result(operation_id)
    # status: "win" or "loss"
    # operation_info: complete operation details
```

### 6.2 Real-time Verification
```python
async def check_win(id_number):
    result = await client.check_win(id_number)
    # result: True for profit, False for loss
```

## 7. Balance Management

### 7.1 Get Balance
```python
async def get_balance():
    balance = await client.get_balance()
    print(f"Current balance: {balance}")
```

### 7.2 Demo Balance Refill
```python
async def balance_refill():
    result = await client.edit_practice_balance(5000)
    # Refills demo account with 5000
```

### 7.3 Switching Between Demo and Real Account
```python
# Switch to real account
client.set_account_mode("REAL")

# Switch to demo account
client.set_account_mode("PRACTICE")
```

## Important Considerations

1. All operations are asynchronous and require the use of `await`
2. It's recommended to verify that the asset is available before trading
3. Expiration times are in seconds
4. Valid directions are "call" (up) and "put" (down)
5. Always verify connection status before operating
6. It's important to handle errors properly in production
---
title: Documentação do pyquotex - Português
description: Documentação completa do pyquotex em português.
---

# Documentação do pyquotex (Português)

Bem-vindo à documentação do pyquotex!

## Seções

- [Instalação e Configuração](1.%20Instalação%20e%20Configuração)
- [Conexão e Autenticação](2.%20Conexão%20e%20Autenticação)
- [Operações de Trading](3.%20Operações%20de%20Trading)
- [Recuperação de Dados do Mercado](4.%20Obtenção%20de%20Dados%20do%20Mercado)
- [Gerenciamento de Conta](5.%20Gestão%20de%20Conta)
- [Indicadores Técnicos](6.%20Indicadores%20Técnicos)
- [WebSocket](7.%20WebSocket)
- [Utilitários e Helpers](8.%20Utilitários%20e%20Helpers)
- [Exemplos Básicos](9.%20Exemplos%20Básicos)
- [Aspectos Técnicos](10.%20Aspectos%20Técnicos)
- [Considerações e Avisos](11.%20Considerações%20e%20Advertências)

---

pyquotex é uma biblioteca Python projetada para integrar com a API da Quotex, permitindo operações automatizadas com facilidade.

# Documentação PyQuotex

Esta documentação cobre os principais casos de uso e exemplos de implementação da biblioteca PyQuotex para interagir com a plataforma Quotex.

## Exemplos Básicos

### Inicialização e Conexão

```python
from pyquotex.stable_api import Quotex

# Inicializar o cliente
client = Quotex(
    email="<EMAIL>",
    password="sua_senha",
    lang="pt"  # Idioma padrão
)


# Conectar ao servidor
async def conectar():
    check_connect, message = await client.connect()
    if check_connect:
        print("Conectado com sucesso")
        balance = await client.get_balance()
        print(f"Saldo atual: {balance}")
    await cliente.close()
```

### Obter Informações do Perfil

```python
async def obter_perfil():
    check_connect, message = await client.connect()
    if check_connect:
        profile = await client.get_profile()
        print(f"""
        Usuário: {profile.nick_name}
        Sal<PERSON> De<PERSON>: {profile.demo_balance}
        Saldo Real: {profile.live_balance}
        País: {profile.country_name}
        """)
    await cliente.close()
```

## Casos de Uso Comuns

### 1. Realizar uma Operação Simples

```python
async def operar():
    check_connect, message = await client.connect()
    if check_connect:
        # Parâmetros da operação
        amount = 50  # Quantidade
        asset = "EURUSD_otc"  # Par de moedas
        direction = "call"  # call=subida, put=descida
        duration = 60  # Duração em segundos

        # Verificar se o ativo está disponível
        asset_name, asset_data = await client.get_available_asset(asset, force_open=True)

        if asset_data[2]:  # Verificar se o mercado está aberto
            status, buy_info = await client.buy(amount, asset_name, direction, duration)
            if status:
                # Esperar resultado
                win = await client.check_win(buy_info["id"])
                profit = client.get_profit()
                print(f"Resultado: {'Ganho' if win else 'Perda'} de {profit}")

    await cliente.close()
```

### 2. Monitoramento de Preços em Tempo Real

```python
async def monitorar_precos():
    check_connect, message = await client.connect()
    if check_connect:
        asset = "EURUSD_otc"

        # Iniciar stream de preços
        await client.start_realtime_price(asset, 60)

        # Monitorar continuamente
        while True:
            precios = await client.get_realtime_price(asset)
            if precios:
                ultimo_precio = precios[-1]
                print(f"Tempo: {ultimo_precio['time']} Preço: {ultimo_precio['price']}")
            await asyncio.sleep(1)
```

## Implementações Recomendadas

### 1. Gestão de Erros e Reconexão

```python
async def conectar_com_reintentos(max_intentos=5):
    intentos = 0
    while intentos < max_intentos:
        try:
            check_connect, message = await client.connect()
            if check_connect:
                return True
            intentos += 1
            print(f"Reintentando conexão ({intentos}/{max_intentos})")
            await asyncio.sleep(5)
        except Exception as e:
            print(f"Erro de conexão: {e}")
            intentos += 1
    return False
```

### 2. Classe Base para Trading

```python
class TradingBot:
    def __init__(self, email, password):
        self.client = Quotex(email=email, password=password)
        self.connected = False

    async def iniciar(self):
        self.connected = await self.conectar_com_reintentos()
        if self.connected:
            await self.configurar_conta()

    async def configurar_conta(self):
        # Configurar modo demo por padrão
        self.client.set_account_mode("PRACTICE")

    async def fechar(self):
        await self.client.close()
        self.connected = False
```

## Scripts de Exemplo

### 1. Bot Básico de Trading

```python
import asyncio
from pyquotex.stable_api import Quotex


class SimpleBot:
    def __init__(self):
        self.client = Quotex(
            email="<EMAIL>",
            password="sua_senha"
        )
        self.running = False

    async def iniciar(self):
        check_connect, _ = await self.client.connect()
        if check_connect:
            self.running = True
            await self.trading_loop()

    async def trading_loop(self):
        while self.running:
            try:
                # Obter dados de mercado
                sentiment = await self.client.get_realtime_sentiment("EURUSD_otc")
                if sentiment.get("sentiment", {}).get("buy", 0) > 70:
                    # Executar operação se o sentimento for muito alcista
                    await self.executar_operacao("EURUSD_otc", "call", 50, 60)

                await asyncio.sleep(60)  # Esperar 1 minuto
            except Exception as e:
                print(f"Erro no loop de trading: {e}")
                await asyncio.sleep(5)

    async def executar_operacao(self, asset, direction, amount, duration):
        status, buy_info = await self.client.buy(amount, asset, direction, duration)
        if status:
            resultado = await self.client.check_win(buy_info["id"])
            print(f"Operação {'ganhadora' if resultado else 'perdedora'}")

    async def parar(self):
        self.running = False
        await self.client.close()


# Uso do bot
async def main():
    bot = SimpleBot()
    await bot.iniciar()


if __name__ == "__main__":
    asyncio.run(main())
```

### 2. Monitor de Mercado

```python
import asyncio
import datetime

async def monitor_mercado():
    client = Quotex(
        email="<EMAIL>",
        password="sua_senha"
    )

    check_connect, _ = await client.connect()
    if check_connect:
        ativos = ["EURUSD", "GBPUSD", "USDJPY"]

        while True:
            for ativo in ativos:
                try:
                    # Obter dados do ativo
                    precio = await client.get_realtime_price(ativo)
                    sentiment = await client.get_realtime_sentiment(ativo)

                    # Salvar ou mostrar informação
                    timestamp = datetime.datetime.now()
                    print(f"""
                    {timestamp} - {ativo}:
                    Preço: {precio[-1]['price'] if precio else 'N/A'}
                    Sentimento Compra: {sentiment.get('sentiment', {}).get('buy', 'N/A')}%
                    Sentimento Venda: {sentiment.get('sentiment', {}).get('sell', 'N/A')}%
                    """)

                except Exception as e:
                    print(f"Erro monitorando {ativo}: {e}")

            await asyncio.sleep(5)  # Atualizar a cada 5 segundos

if __name__ == "__main__":
    asyncio.run(monitor_mercado())
```

## Considerações Importantes

1. **Gestão de Risco**: Sempre implementar controles de risco e limites de perdas.
2. **Modo Demo**: Testar estratégias primeiro no modo demo antes de usar dinheiro real.
3. **Manejo de Erros**: Implementar manejo robusto de erros e reconexões.
4. **Limitações da API**: Considerar as limitações e tempos de espera da API.

## Notas de Segurança

- Nunca armazenar credenciais no código
- Usar variáveis de ambiente ou arquivos de configuração seguros
- Implementar timeouts e manejo de erros
- Manter registros de operações e erros

#!/usr/bin/env python3
"""
Echo Sniper Strategy Engine for Trading Bot
Pattern-based trading using historical candle pattern analysis
"""

import pandas as pd
import numpy as np
from datetime import datetime
from config import STRATEGY_CONFIG, TRADING_CONFIG, ECHO_SNIPER_CONFIG
from utils import print_colored, format_price

class StrategyEngine:
    def __init__(self, echo_sniper_config=None):
        """Initialize the strategy engine with Echo Sniper configuration"""
        self.strategies = STRATEGY_CONFIG
        self.echo_sniper_config = echo_sniper_config or ECHO_SNIPER_CONFIG.copy()

    def get_candle_direction(self, candle):
        """Determine candle direction: 'green' or 'red' (no doji for simplicity)"""
        # Use a small threshold to avoid doji classification
        price_diff = candle['close'] - candle['open']

        if price_diff > 0:
            return 'green'
        else:
            return 'red'  # Treat doji as red for pattern consistency
    
    def detect_pattern(self, df, pattern_length):
        """Detect pattern from last N closed candles (excluding current running candle)"""
        try:
            # Use only closed candles (exclude current running candle)
            closed_candles = df.iloc[:-1]  # Exclude last (current) candle
            
            if len(closed_candles) < pattern_length:
                return None, "Insufficient closed candles for pattern detection"
            
            # Get last N closed candles for pattern
            pattern_candles = closed_candles.tail(pattern_length)
            pattern = []
            
            for _, candle in pattern_candles.iterrows():
                direction = self.get_candle_direction(candle)
                pattern.append(direction)
            
            return pattern, None
            
        except Exception as e:
            return None, f"Error detecting pattern: {str(e)}"
    
    def find_historical_patterns(self, df, target_pattern, historical_candles):
        """Find all occurrences of target pattern in historical data"""
        try:
            matches = []
            pattern_length = len(target_pattern)
            
            # Search through historical data (excluding recent candles used for current pattern)
            search_data = df.iloc[:-pattern_length-1]  # Exclude current pattern and running candle
            
            if len(search_data) < historical_candles:
                historical_candles = len(search_data)
            
            # Limit search to specified historical candles
            if historical_candles > 0:
                search_data = search_data.tail(historical_candles)
            
            # Search for pattern matches
            for i in range(len(search_data) - pattern_length + 1):
                # Extract pattern at current position
                pattern_candles = search_data.iloc[i:i + pattern_length]
                current_pattern = []
                
                for _, candle in pattern_candles.iterrows():
                    direction = self.get_candle_direction(candle)
                    current_pattern.append(direction)
                
                # Check if pattern matches
                if current_pattern == target_pattern:
                    # Get the next candle after the pattern (if exists)
                    next_index = i + pattern_length
                    if next_index < len(search_data):
                        next_candle = search_data.iloc[next_index]
                        next_direction = self.get_candle_direction(next_candle)
                        matches.append(next_direction)
            
            return matches, None
            
        except Exception as e:
            return [], f"Error finding historical patterns: {str(e)}"
    
    def calculate_pattern_statistics(self, matches):
        """Calculate win rate and statistics from pattern matches"""
        try:
            if not matches:
                return 0.0, 0, 0, 0, None
            
            total_matches = len(matches)
            green_wins = matches.count('green')
            red_wins = matches.count('red')
            
            # Determine expected direction based on majority
            if green_wins > red_wins:
                expected_direction = 'green'
                win_rate = green_wins / total_matches
            elif red_wins > green_wins:
                expected_direction = 'red'
                win_rate = red_wins / total_matches
            else:
                # Equal or no clear winner
                expected_direction = None
                win_rate = 0.5
            
            return win_rate, total_matches, green_wins, red_wins, expected_direction
            
        except Exception as e:
            print_colored(f"❌ Error calculating pattern statistics: {str(e)}", "ERROR")
            return 0.0, 0, 0, 0, None
    
    def check_pattern_completion(self, df, expected_direction):
        """Check if current candle already moved in expected direction"""
        try:
            current_candle = df.iloc[-1]
            current_direction = self.get_candle_direction(current_candle)
            
            # If current candle already moved as expected, pattern is completed
            if current_direction == expected_direction:
                return True, f"Pattern already completed - current candle is {current_direction}"
            
            return False, "Pattern not yet completed"
            
        except Exception as e:
            return True, f"Error checking pattern completion: {str(e)}"
    
    def calculate_confidence_score(self, win_rate, total_matches):
        """Calculate confidence score based on win rate and number of occurrences"""
        try:
            thresholds = self.echo_sniper_config['confidence_thresholds']
            
            # High confidence
            if (win_rate >= thresholds['high']['min_win_rate'] and 
                total_matches >= thresholds['high']['min_occurrences']):
                return 'high', min(0.90, 0.70 + (win_rate - 0.75) * 0.8)
            
            # Medium confidence  
            elif (win_rate >= thresholds['medium']['min_win_rate'] and 
                  total_matches >= thresholds['medium']['min_occurrences']):
                return 'medium', min(0.80, 0.60 + (win_rate - 0.65) * 0.8)
            
            # Low confidence
            elif (win_rate >= thresholds['low']['min_win_rate'] and 
                  total_matches >= thresholds['low']['min_occurrences']):
                return 'low', min(0.70, 0.50 + (win_rate - 0.55) * 0.8)
            
            # Below threshold
            else:
                return 'none', 0.0
                
        except Exception as e:
            print_colored(f"❌ Error calculating confidence: {str(e)}", "ERROR")
            return 'none', 0.0

    def evaluate_echo_sniper_strategy(self, df):
        """
        Echo Sniper Strategy: Pattern-based trading using historical candle pattern analysis

        Requirements Implementation:
        1. Pattern Length (N): User configurable number of candles that make up the pattern
        2. Historical Candles Analysis: Min 10, Max 300 candles to find pattern occurrences
        3. Fetch Candles for Accuracy: Min 20, Max 500 candles to test pattern accuracy
        4. Minimum Win Rate: Optional threshold (e.g., 70%)
        5. Uses only closed candles (ignores current running candle)
        6. Pattern detection based on red/green candle directions
        7. Signal generation only if pattern hasn't already completed
        8. Confidence scoring based on historical performance

        Steps:
        1. Fetch market data at candle close (e.g., 12:30:00 when new candle opens)
        2. Detect pattern from last N closed candles (excluding current running candle)
        3. Search historical data for same pattern occurrences
        4. Calculate win rate based on next candle outcomes after pattern
        5. Check if current candle already fulfilled expected move
        6. Generate signal only if pattern hasn't already completed
        7. Apply confidence scoring and minimum win rate threshold
        """
        try:
            config = self.echo_sniper_config
            pattern_length = config['pattern_length']
            historical_candles = config['historical_candles']
            min_win_rate = config.get('min_win_rate', 0.0)

            # Minimum data requirement: pattern + historical + current candle
            min_required = pattern_length + historical_candles + 1
            if len(df) < min_required:
                print_colored(f"⚠️ Echo Sniper: Insufficient data ({len(df)} < {min_required} candles)", "WARNING")
                return 0, 0.0

            # Step 1: Detect current pattern from closed candles (exclude running candle)
            pattern, error = self.detect_pattern(df, pattern_length)
            if error:
                return 0, 0.0

            # Step 2: Find historical occurrences of this exact pattern
            matches, error = self.find_historical_patterns(df, pattern, historical_candles)
            if error or not matches:
                return 0, 0.0

            # Step 3: Calculate pattern statistics and expected direction
            win_rate, total_matches, green_wins, red_wins, expected_direction = self.calculate_pattern_statistics(matches)

            # Step 4: Check minimum win rate threshold
            if win_rate < min_win_rate:
                print_colored(f"🎯 Echo Sniper: Win rate {win_rate:.1%} below threshold {min_win_rate:.1%}", "WARNING")
                return 0, 0.0

            if not expected_direction:
                return 0, 0.0

            # Step 5: Check if pattern already completed (current candle moved as expected)
            completed, _ = self.check_pattern_completion(df, expected_direction)
            if completed:
                return 0, 0.0

            # Step 6: Calculate confidence score based on historical performance
            confidence_level, confidence_score = self.calculate_confidence_score(win_rate, total_matches)

            if confidence_level == 'none':
                return 0, 0.0

            # Step 7: Generate trading signal
            if expected_direction == 'green':
                signal = 1  # CALL signal
            else:  # expected_direction == 'red'
                signal = -1  # PUT signal

            return signal, confidence_score

        except Exception as e:
            print_colored(f"❌ Error in Echo Sniper Strategy: {str(e)}", "ERROR")
            return 0, 0.0

    def evaluate_all_strategies(self, df, selected_strategies=None):
        """Evaluate Echo Sniper strategy (only strategy available)"""
        # Echo Sniper is the only strategy now
        signal, confidence = self.evaluate_echo_sniper_strategy(df)

        # Return result in expected format
        if signal != 0 and confidence >= TRADING_CONFIG.get('MIN_CONFIDENCE', 0.5):
            signal_name = 'BUY' if signal == 1 else 'SELL'
            return {
                'signal': signal_name,
                'confidence': confidence,
                'strategy': 'ECHO_SNIPER',
                'price': df.iloc[-1]['close'],
                'all_signals': {'ECHO_SNIPER': {'signal': signal, 'confidence': confidence}}
            }
        else:
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'strategy': None,
                'price': df.iloc[-1]['close'],
                'all_signals': {'ECHO_SNIPER': {'signal': signal, 'confidence': confidence}}
            }

    def calculate_atr(self, df, period=14):
        """Calculate Average True Range (utility method)"""
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        return tr.rolling(period).mean()

    def calculate_rsi_custom(self, df, period=14):
        """Calculate RSI if not available (utility method)"""
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.rolling(period).mean()
        avg_loss = loss.rolling(period).mean()

        rs = avg_gain / avg_loss
        return 100 - (100 / (1 + rs))

    def update_echo_sniper_config(self, new_config):
        """Update Echo Sniper configuration"""
        self.echo_sniper_config.update(new_config)
        print_colored(f"🔧 Echo Sniper config updated: {new_config}", "SUCCESS")

    def get_pattern_info(self, df):
        """Get current pattern information for logging"""
        try:
            pattern, error = self.detect_pattern(df, self.echo_sniper_config['pattern_length'])
            if error:
                return None

            matches, error = self.find_historical_patterns(df, pattern, self.echo_sniper_config['historical_candles'])
            if error:
                return None

            win_rate, total_matches, green_wins, red_wins, expected_direction = self.calculate_pattern_statistics(matches)

            return {
                'pattern': pattern,
                'total_matches': total_matches,
                'green_wins': green_wins,
                'red_wins': red_wins,
                'win_rate': win_rate,
                'expected_direction': expected_direction
            }
        except Exception as e:
            print_colored(f"❌ Error getting pattern info: {str(e)}", "ERROR")
            return None

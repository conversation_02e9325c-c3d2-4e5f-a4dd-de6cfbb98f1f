#!/usr/bin/env python3
"""
Ultra-Fast PyQuotex Trading - Direct API Implementation
Uses PyQuotex's direct buy() method for instant trade execution
"""

import asyncio
import time
import sys
import os

# Add PyQuotex to path
parent_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, parent_dir)

from pyquotex.stable_api import Quotex

class UltraFastQuotex:
    """Ultra-fast PyQuotex trading using direct API"""
    
    def __init__(self, email, password, demo_mode=True):
        self.email = email
        self.password = password
        self.demo_mode = demo_mode
        self.client = None
        self.connected = False
    
    async def connect(self):
        """Connect to PyQuotex with ultra-fast setup"""
        try:
            print("⚡ ULTRA-FAST QUOTEX CONNECTION...")
            
            start_time = time.time()
            
            # Create PyQuotex client
            self.client = Quotex(
                email=self.email,
                password=self.password,
                lang="pt"
            )
            
            # Connect
            connected = await self.client.connect()
            
            if connected:
                # Set demo mode if needed
                if self.demo_mode:
                    await self.client.change_account("demo")
                
                connection_time = time.time() - start_time
                print(f"⚡ CONNECTED in {connection_time:.2f}s")
                
                self.connected = True
                return True
            else:
                print("❌ Connection failed")
                return False
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    async def get_balance(self):
        """Get account balance"""
        try:
            if not self.connected:
                return 0.0
            
            balance = await self.client.get_balance()
            return balance
            
        except Exception as e:
            print(f"❌ Balance error: {e}")
            return 0.0
    
    async def ultra_fast_trade(self, action, amount, asset, duration):
        """Ultra-fast trade execution using PyQuotex direct API"""
        try:
            print(f"⚡ ULTRA-FAST TRADE: {action.upper()} {asset} ${amount} {duration}s")
            
            if not self.connected:
                print("❌ Not connected")
                return False, "Not connected"
            
            # Convert action to direction
            direction = "call" if action.lower() == "call" else "put"
            
            # Remove _otc suffix for API
            api_asset = asset.replace("_otc", "")
            
            # Execute trade with timing
            start_time = time.time()
            
            # Use PyQuotex's direct buy method
            success, result = await self.client.buy(amount, api_asset, direction, duration)
            
            execution_time = time.time() - start_time
            
            print(f"⏱️ Execution time: {execution_time:.2f}s")
            
            if success:
                print(f"🎉 ULTRA-FAST TRADE SUCCESS: {direction.upper()} {api_asset}")
                return True, f"Ultra-fast trade successful: {direction.upper()} {api_asset}"
            else:
                print(f"❌ Trade failed: {result}")
                return False, f"Trade failed: {result}"
                
        except Exception as e:
            print(f"❌ Ultra-fast trade error: {e}")
            return False, str(e)
    
    async def close(self):
        """Close connection"""
        try:
            if self.client:
                await self.client.close()
                self.connected = False
                print("🔌 Connection closed")
        except Exception as e:
            print(f"❌ Close error: {e}")

async def test_ultra_fast_trading():
    """Test ultra-fast trading"""
    print("⚡ TESTING ULTRA-FAST PYQUOTEX TRADING")
    print("=" * 60)
    
    # Credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Phase 1: Ultra-Fast Connection
        print("\n⚡ PHASE 1: ULTRA-FAST CONNECTION")
        print("-" * 40)
        
        client = UltraFastQuotex(email, password, demo_mode=True)
        connected = await client.connect()
        
        if not connected:
            print("❌ Connection failed")
            return
        
        # Phase 2: Balance Check
        print("\n💰 PHASE 2: BALANCE CHECK")
        print("-" * 40)
        
        balance = await client.get_balance()
        print(f"💰 Balance: ${balance}")
        
        # Phase 3: Ultra-Fast Trading
        print("\n🚀 PHASE 3: ULTRA-FAST TRADING")
        print("-" * 40)
        
        # Test trades
        test_trades = [
            ("call", 10.0, "EURUSD", 60),
            ("put", 10.0, "EURUSD", 60)
        ]
        
        for action, amount, asset, duration in test_trades:
            print(f"\n🚀 Testing {action.upper()} trade...")
            
            success, result = await client.ultra_fast_trade(action, amount, asset, duration)
            
            if success:
                print(f"✅ Trade result: {result}")
            else:
                print(f"❌ Trade failed: {result}")
            
            # Wait between trades
            await asyncio.sleep(2)
        
        # Phase 4: Final Balance
        print("\n💰 PHASE 4: FINAL BALANCE")
        print("-" * 40)
        
        final_balance = await client.get_balance()
        print(f"💰 Final balance: ${final_balance}")
        
        print("\n🎉 ULTRA-FAST TRADING TEST COMPLETED!")
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            await client.close()

if __name__ == "__main__":
    asyncio.run(test_ultra_fast_trading())

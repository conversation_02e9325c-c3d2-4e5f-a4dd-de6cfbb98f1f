#!/usr/bin/env python3
"""
Test script to verify Model.py is working correctly after fixes
"""

import sys
import os

def test_imports():
    """Test all critical imports"""
    print("🧪 Testing Critical Imports")
    print("=" * 40)
    
    try:
        # Test quotex_integration import
        from quotex_integration import QuotexBotIntegration, get_quotex_client
        print("✅ quotex_integration imported successfully")
        
        # Test Model import
        import Model
        print("✅ Model.py imported successfully")
        
        # Test strategy_engine import
        from strategy_engine import StrategyEngine
        print("✅ strategy_engine imported successfully")
        
        # Test signal_logger import
        from signal_logger import save_signal, evaluate_last_signal
        print("✅ signal_logger imported successfully")
        
        # Test utils import
        from utils import print_colored
        print("✅ utils imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_quotex_integration_class():
    """Test QuotexBotIntegration class instantiation"""
    print(f"\n🧪 Testing QuotexBotIntegration Class")
    print("=" * 40)
    
    try:
        from quotex_integration import QuotexBotIntegration
        
        # Test class instantiation (without connecting)
        integration = QuotexBotIntegration("<EMAIL>", "password", demo_mode=True)
        print("✅ QuotexBotIntegration class instantiated successfully")
        
        # Test method existence
        methods_to_check = [
            'switch_trading_pair',
            'trade',
            'connect',
            'get_balance'
        ]
        
        for method_name in methods_to_check:
            if hasattr(integration, method_name):
                print(f"✅ Method '{method_name}' exists")
            else:
                print(f"❌ Method '{method_name}' missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ QuotexBotIntegration error: {e}")
        return False

def test_strategy_engine():
    """Test StrategyEngine class"""
    print(f"\n🧪 Testing StrategyEngine Class")
    print("=" * 40)
    
    try:
        from strategy_engine import StrategyEngine
        
        # Test class instantiation
        engine = StrategyEngine()
        print("✅ StrategyEngine instantiated successfully")
        
        # Test method existence
        methods_to_check = [
            'evaluate_echo_sniper_strategy',
            'get_pattern_info'
        ]
        
        for method_name in methods_to_check:
            if hasattr(engine, method_name):
                print(f"✅ Method '{method_name}' exists")
            else:
                print(f"❌ Method '{method_name}' missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ StrategyEngine error: {e}")
        return False

def test_signal_logger():
    """Test signal logger functions"""
    print(f"\n🧪 Testing Signal Logger Functions")
    print("=" * 40)
    
    try:
        from signal_logger import save_signal, evaluate_last_signal, initialize_signal_logger
        
        # Test initialization
        initialize_signal_logger()
        print("✅ Signal logger initialized successfully")
        
        # Test signal saving (mock data)
        test_signal = {
            "pair": "TEST_otc",
            "timestamp": "12:00:00",
            "date": "2025-07-19",
            "direction": "put",
            "price": 1.0000,
            "strategy_used": "ECHO_SNIPER",
            "result": "pending"
        }
        
        result = save_signal(test_signal)
        if result:
            print("✅ Signal saving works")
        else:
            print("❌ Signal saving failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Signal logger error: {e}")
        return False

def test_model_functions():
    """Test key Model.py functions"""
    print(f"\n🧪 Testing Model.py Functions")
    print("=" * 40)
    
    try:
        import Model
        
        # Test function existence
        functions_to_check = [
            'fetch_quotex_market_data',
            'generate_signal',
            'validate_data_quality'
        ]
        
        for func_name in functions_to_check:
            if hasattr(Model, func_name):
                print(f"✅ Function '{func_name}' exists")
            else:
                print(f"❌ Function '{func_name}' missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Model functions error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Model.py Working Status")
    print("=" * 60)
    
    results = []
    
    # Test 1: Imports
    results.append(test_imports())
    
    # Test 2: QuotexBotIntegration class
    results.append(test_quotex_integration_class())
    
    # Test 3: StrategyEngine class
    results.append(test_strategy_engine())
    
    # Test 4: Signal logger
    results.append(test_signal_logger())
    
    # Test 5: Model functions
    results.append(test_model_functions())
    
    # Summary
    print(f"\n🎯 Test Results Summary")
    print("=" * 30)
    
    test_names = [
        "Critical Imports",
        "QuotexBotIntegration Class",
        "StrategyEngine Class", 
        "Signal Logger Functions",
        "Model.py Functions"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
    
    print(f"\n📊 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Model.py is working correctly!")
        print("✅ All syntax errors fixed")
        print("✅ All imports working")
        print("✅ All classes instantiating")
        print("✅ All functions accessible")
        print("\n🚀 Ready to run: python Model.py")
    else:
        print("⚠️ Some issues still need attention")
    
    return passed == total

if __name__ == "__main__":
    main()

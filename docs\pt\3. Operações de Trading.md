# Documentação de Operações de Trading PyQuotex

Esta documentação abrange as principais operações de trading disponíveis na API do PyQuotex.

## 1. Compra Simples (Buy)

A operação de compra simples permite realizar uma transação básica especificando o valor, ativo, direção e duração.

```python
async def buy_simple():
    amount = 50  # Valor na moeda da conta
    asset = "AUDCAD"  # Código do ativo
    direction = "call"  # Direção: "call" (alta) ou "put" (baixa)
    duration = 60  # Duração em segundos
    
    status, buy_info = await client.buy(amount, asset_name, direction, duration)
    # status: True/False indicando se a operação foi bem-sucedida
    # buy_info: Informação detalhada da ordem
```

## 2. Compra com Verificação de Resultado

Esta operação permite realizar uma compra e aguardar automaticamente o resultado:

```python
async def buy_and_check_win():
    amount = 50
    asset = "EURUSD_otc"
    direction = "call"
    duration = 60
    
    status, buy_info = await client.buy(amount, asset_name, direction, duration)
    if status:
        # Aguardar o resultado
        if await client.check_win(buy_info["id"]):
            profit = client.get_profit()
            print(f"Ganhou! Lucro: {profit}")
        else:
            loss = client.get_profit()
            print(f"Perdeu: {loss}")
```

## 3. Compras Múltiplas

Permite realizar múltiplas operações sequenciais:

```python
order_list = [
    {"amount": 5, "asset": "EURUSD", "direction": "call", "duration": 60},
    {"amount": 10, "asset": "AUDCAD_otc", "direction": "put", "duration": 60},
    # ... mais ordens
]

async def buy_multiple(orders=10):
    for i in range(orders):
        order = random.choice(order_list)
        status, buy_info = await client.buy(**order)
```

## 4. Ordens Pendentes

As ordens pendentes permitem programar operações para serem executadas em um momento específico:

```python
async def buy_pending():
    amount = 50
    asset = "AUDCAD"
    direction = "call"
    duration = 60
    open_time = "16/12 15:51"  # Formato: "dd/mm HH:MM"
    
    status, buy_info = await client.open_pending(
        amount, 
        asset_name, 
        direction, 
        duration, 
        open_time
    )
```

## 5. Venda de Opções

Permite fechar uma posição antes do vencimento:

```python
async def sell_option():
    # Primeiro abrir uma posição
    status, buy_info = await client.buy(amount, asset_name, direction, duration)
    
    # Depois vender a opção usando seu ID
    result = await client.sell_option(buy_info["id"])
```

## 6. Verificação de Resultados

Existem duas formas de verificar resultados:

### 6.1 Verificação direta
```python
async def check_result(operation_id):
    status, operation_info = await client.get_result(operation_id)
    # status: "win" ou "loss"
    # operation_info: detalhes completos da operação
```

### 6.2 Verificação em tempo real
```python
async def check_win(id_number):
    result = await client.check_win(id_number)
    # result: True para ganho, False para perda
```

## 7. Gestão de Saldo

### 7.1 Obter Saldo
```python
async def get_balance():
    balance = await client.get_balance()
    print(f"Saldo atual: {balance}")
```

### 7.2 Recarga de Saldo Demo
```python
async def balance_refill():
    result = await client.edit_practice_balance(5000)
    # Recarrega a conta demo com 5000
```

### 7.3 Alternar entre Conta Demo e Real
```python
# Mudar para conta real
client.set_account_mode("REAL")

# Mudar para conta demo
client.set_account_mode("PRACTICE")
```

## Considerações Importantes

1. Todas as operações são assíncronas e requerem o uso de `await`
2. É recomendável verificar se o ativo está disponível antes de operar
3. Os tempos de expiração são em segundos
4. As direções válidas são "call" (alta) e "put" (baixa)
5. Sempre verifique o estado da conexão antes de operar
6. É importante tratar adequadamente os erros em produção
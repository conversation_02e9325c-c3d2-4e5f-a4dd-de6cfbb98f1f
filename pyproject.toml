[project]
name = "pyquotex"
version = "1.0.3"
description = "Quotex API Client written in Python."
authors = [
    { name = "cleiton", email = "<EMAIL>"}]
license = "MIT"
readme = "README.md"
packages = [{ include = "quotexapi" }]
requires-python = ">=3.12,<4.0"

dependencies = [
    "websocket-client (>=1.8.0,<2.0.0)",
    "requests (>=2.32.3,<3.0.0)",
    "pyfiglet (>=1.0.2,<2.0.0)",
    "beautifulsoup4 (>=4.12.3,<5.0.0)",
]

[tool.poetry.group.dev.dependencies]
python = ">=3.12,<4.0"
numpy = { version = "^2.2.3", markers = "platform_machine != 'aarch64' and platform_machine != 'armv7l'" }

[build-system]
requires = ["poetry-core>=2.0.0"]
build-backend = "poetry.core.masonry.api"

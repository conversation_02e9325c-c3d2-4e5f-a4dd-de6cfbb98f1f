#!/usr/bin/env python3
"""
Comprehensive test to verify the bot only uses real market data
"""

import asyncio
import sys
import os
import time

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

from Model import fetch_quotex_market_data, generate_signal, convert_quotex_to_oanda_pair
from utils import print_colored, fetch_live_candles
from strategy_engine import StrategyEngine

async def test_real_data_fetching():
    """Test that only real data is fetched, no fallback"""
    print_colored("🧪 Testing Real Data Fetching System", "HEADER", bold=True)
    print_colored("=" * 60, "HEADER")
    
    # Test Live pairs (should use Oanda API)
    live_pairs = ["USDJPY", "EURUSD", "GBPUSD", "AUDUSD"]
    success_count = 0
    
    for pair in live_pairs:
        print_colored(f"\n🔍 Testing {pair} (Live pair - Oanda API)...", "INFO")
        
        try:
            df = await fetch_quotex_market_data(pair, "M1", 20)
            
            if df is not None and len(df) > 0:
                latest_price = df['close'].iloc[-1]
                print_colored(f"✅ {pair}: {len(df)} real candles, price: {latest_price:.5f}", "SUCCESS")
                success_count += 1
            else:
                print_colored(f"❌ {pair}: No real data returned (correctly skipped)", "WARNING")
                
        except Exception as e:
            print_colored(f"❌ {pair}: Error - {str(e)}", "ERROR")
    
    print_colored(f"\n📊 Live pairs test: {success_count}/{len(live_pairs)} successful", 
                 "SUCCESS" if success_count > 0 else "WARNING")
    
    return success_count > 0

async def test_signal_generation_with_real_data():
    """Test signal generation only works with real data"""
    print_colored("\n🎯 Testing Signal Generation with Real Data Only", "HEADER", bold=True)
    print_colored("=" * 60, "HEADER")
    
    strategy_engine = StrategyEngine()
    test_pairs = ["USDJPY", "EURUSD"]
    signals_generated = 0
    
    for pair in test_pairs:
        print_colored(f"\n🔍 Testing signal generation for {pair}...", "INFO")
        
        try:
            signal, confidence, price, strategy = await generate_signal(
                pair, strategy_engine, ["S4"], "M1", 10, 20
            )
            
            if signal != "hold" and confidence > 0:
                print_colored(f"✅ {pair}: Signal={signal}, Confidence={confidence:.1%}, Price={price:.5f}, Strategy={strategy}", "SUCCESS")
                signals_generated += 1
            elif price > 0:  # Real data was fetched but signal was hold
                print_colored(f"📊 {pair}: Real data fetched (price={price:.5f}) but signal=HOLD", "INFO")
            else:
                print_colored(f"⚠️ {pair}: No real data available - signal generation skipped", "WARNING")
                
        except Exception as e:
            print_colored(f"❌ {pair}: Signal generation error - {str(e)}", "ERROR")
    
    print_colored(f"\n📊 Signal generation test: {signals_generated} signals from real data", "SUCCESS")
    return True

def test_oanda_api_direct():
    """Test Oanda API directly"""
    print_colored("\n🔗 Testing Oanda API Direct Connection", "HEADER", bold=True)
    print_colored("=" * 50, "HEADER")
    
    test_instruments = ["USD_JPY", "EUR_USD", "GBP_USD"]
    success_count = 0
    
    for instrument in test_instruments:
        print_colored(f"\n🔍 Testing {instrument}...", "INFO")
        
        try:
            df = fetch_live_candles(instrument, count=10, granularity="M1")
            
            if df is not None and len(df) > 0:
                latest_price = df['close'].iloc[-1]
                latest_time = df.index[-1] if hasattr(df.index[-1], 'strftime') else 'N/A'
                
                print_colored(f"✅ {instrument}: {len(df)} candles", "SUCCESS")
                print_colored(f"💰 Latest price: {latest_price:.5f}", "SUCCESS")
                print_colored(f"🕐 Latest time: {latest_time}", "SUCCESS")
                success_count += 1
            else:
                print_colored(f"❌ {instrument}: No data returned", "ERROR")
                
        except Exception as e:
            print_colored(f"❌ {instrument}: Error - {str(e)}", "ERROR")
    
    print_colored(f"\n📊 Oanda API test: {success_count}/{len(test_instruments)} successful", 
                 "SUCCESS" if success_count == len(test_instruments) else "WARNING")
    
    return success_count == len(test_instruments)

def test_pair_mapping():
    """Test currency pair mapping"""
    print_colored("\n🗺️ Testing Currency Pair Mapping", "HEADER", bold=True)
    print_colored("=" * 40, "HEADER")
    
    test_mappings = {
        "USDJPY": "USD_JPY",
        "EURUSD": "EUR_USD", 
        "GBPUSD": "GBP_USD",
        "AUDUSD": "AUD_USD"
    }
    
    success_count = 0
    
    for quotex_pair, expected_oanda in test_mappings.items():
        oanda_pair = convert_quotex_to_oanda_pair(quotex_pair)
        
        if oanda_pair == expected_oanda:
            print_colored(f"✅ {quotex_pair} → {oanda_pair}", "SUCCESS")
            success_count += 1
        else:
            print_colored(f"❌ {quotex_pair} → {oanda_pair} (expected {expected_oanda})", "ERROR")
    
    print_colored(f"\n📊 Mapping test: {success_count}/{len(test_mappings)} correct", 
                 "SUCCESS" if success_count == len(test_mappings) else "ERROR")
    
    return success_count == len(test_mappings)

async def test_no_fallback_data():
    """Test that fallback data is never used"""
    print_colored("\n🚫 Testing No Fallback Data Policy", "HEADER", bold=True)
    print_colored("=" * 45, "HEADER")
    
    # Test with a non-existent pair to ensure no fallback
    fake_pair = "FAKEPAIR"
    
    print_colored(f"🔍 Testing with fake pair: {fake_pair}", "INFO")
    
    try:
        df = await fetch_quotex_market_data(fake_pair, "M1", 10)
        
        if df is None:
            print_colored("✅ Correctly returned None for fake pair (no fallback data)", "SUCCESS")
            return True
        else:
            print_colored("❌ ERROR: Fallback data was generated for fake pair!", "ERROR")
            return False
            
    except Exception as e:
        print_colored(f"✅ Correctly failed for fake pair: {str(e)}", "SUCCESS")
        return True

async def main():
    """Run all real data system tests"""
    print_colored("🚀 REAL DATA SYSTEM COMPREHENSIVE TEST", "CYAN", bold=True)
    print_colored("=" * 70, "CYAN", bold=True)
    print_colored("🎯 Verifying bot ONLY uses real market data", "CYAN")
    print_colored("🚫 Confirming NO fallback/synthetic data is used", "CYAN")
    print_colored("=" * 70, "CYAN", bold=True)
    
    tests = [
        ("Oanda API Direct", test_oanda_api_direct),
        ("Currency Pair Mapping", test_pair_mapping),
        ("Real Data Fetching", test_real_data_fetching),
        ("Signal Generation", test_signal_generation_with_real_data),
        ("No Fallback Policy", test_no_fallback_data),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print_colored(f"\n{'='*20} {test_name} {'='*20}", "CYAN")
            if await test_func() if asyncio.iscoroutinefunction(test_func) else test_func():
                passed += 1
                print_colored(f"✅ {test_name}: PASSED", "SUCCESS")
            else:
                print_colored(f"❌ {test_name}: FAILED", "ERROR")
        except Exception as e:
            print_colored(f"❌ {test_name}: CRASHED - {e}", "ERROR")
    
    print_colored("\n" + "=" * 70, "CYAN", bold=True)
    print_colored(f"📊 FINAL RESULTS: {passed}/{total} TESTS PASSED", "CYAN", bold=True)
    
    if passed == total:
        print_colored("🎉 ALL TESTS PASSED - REAL DATA SYSTEM WORKING!", "SUCCESS", bold=True)
        print_colored("✅ Bot will ONLY use real market data", "SUCCESS")
        print_colored("✅ No synthetic/fallback data will be used", "SUCCESS")
        print_colored("✅ Oanda API is working correctly", "SUCCESS")
        print_colored("✅ Signal generation requires real data", "SUCCESS")
        print_colored("🚀 READY FOR LIVE TRADING WITH REAL DATA!", "SUCCESS", bold=True)
    else:
        print_colored("⚠️ SOME TESTS FAILED - ISSUES DETECTED", "ERROR", bold=True)
        print_colored("🔧 Fix required before using for live trading", "WARNING")
    
    print_colored("=" * 70, "CYAN", bold=True)
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())

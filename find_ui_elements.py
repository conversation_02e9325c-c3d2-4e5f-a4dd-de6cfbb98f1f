#!/usr/bin/env python3
"""
Find UI Elements - Take screenshot and analyze the Quotex interface
"""

import asyncio
import sys
import os
import time

# Add the train bot directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'train bot'))

from quotex_integration import get_quotex_client

async def find_ui_elements():
    """Find the actual UI elements in Quotex interface"""
    print("🔍 FINDING QUOTEX UI ELEMENTS")
    print("=" * 60)
    
    # Updated credentials
    email = "<EMAIL>"
    password = "Uz2309##2309"
    
    client = None
    
    try:
        # Connect
        print("🔗 Connecting...")
        client = get_quotex_client(email, password, demo_mode=True)
        connected = await client.connect()
        
        if not connected:
            print("❌ Connection failed")
            return
        
        print("✅ Connected successfully")
        
        # Take screenshot
        print("\n📸 Taking screenshot...")
        await client.page.screenshot(path="quotex_interface.png", full_page=True)
        print("✅ Screenshot saved: quotex_interface.png")
        
        # Get page content
        print("\n📄 Analyzing page content...")
        page_content = await client.page.content()
        
        # Look for button-related text
        button_keywords = [
            "UP", "DOWN", "CALL", "PUT", "HIGHER", "LOWER",
            "ACIMA", "ABAIXO", "COMPRAR", "VENDER",
            "BUY", "SELL", "TRADE", "NEGOCIAR"
        ]
        
        found_keywords = []
        for keyword in button_keywords:
            if keyword.lower() in page_content.lower():
                found_keywords.append(keyword)
        
        print(f"🔍 Found keywords in page: {found_keywords}")
        
        # Try to find all buttons
        print("\n🔍 Finding all buttons...")
        buttons = await client.page.query_selector_all('button')
        print(f"📊 Total buttons found: {len(buttons)}")
        
        # Analyze each button
        button_info = []
        for i, button in enumerate(buttons):
            try:
                text = await button.text_content()
                is_visible = await button.is_visible()
                classes = await button.get_attribute('class')
                data_attrs = await button.evaluate('el => Object.keys(el.dataset)')
                
                if text and text.strip():
                    button_info.append({
                        'index': i,
                        'text': text.strip(),
                        'visible': is_visible,
                        'classes': classes,
                        'data_attrs': data_attrs
                    })
            except:
                continue
        
        # Show relevant buttons
        print("\n📋 RELEVANT BUTTONS FOUND:")
        trade_buttons = []
        for btn in button_info:
            text = btn['text'].upper()
            if any(keyword in text for keyword in ['UP', 'DOWN', 'CALL', 'PUT', 'HIGHER', 'LOWER', 'ACIMA', 'ABAIXO']):
                trade_buttons.append(btn)
                print(f"  🎯 Button {btn['index']}: '{btn['text']}' (visible: {btn['visible']})")
                if btn['classes']:
                    print(f"     Classes: {btn['classes']}")
                if btn['data_attrs']:
                    print(f"     Data attrs: {btn['data_attrs']}")
        
        # Try to find trade buttons by other methods
        print("\n🔍 Searching for trade elements...")
        
        # Look for elements with trade-related classes
        trade_classes = [
            '.trade-button', '.trading-button', '.option-button',
            '.call-button', '.put-button', '.up-button', '.down-button',
            '.higher-button', '.lower-button', '.buy-button', '.sell-button'
        ]
        
        for class_name in trade_classes:
            try:
                elements = await client.page.query_selector_all(class_name)
                if elements:
                    print(f"  ✅ Found {len(elements)} elements with class: {class_name}")
            except:
                continue
        
        # Look for elements with specific text
        text_selectors = [
            'text="UP"', 'text="DOWN"', 'text="CALL"', 'text="PUT"',
            'text="HIGHER"', 'text="LOWER"', 'text="ACIMA"', 'text="ABAIXO"'
        ]
        
        for selector in text_selectors:
            try:
                elements = await client.page.query_selector_all(selector)
                if elements:
                    print(f"  ✅ Found {len(elements)} elements with {selector}")
            except:
                continue
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print(f"✅ Screenshot saved: quotex_interface.png")
        print(f"📊 Total buttons: {len(buttons)}")
        print(f"🎯 Trade-related buttons: {len(trade_buttons)}")
        
        if trade_buttons:
            print("\n🎯 RECOMMENDED SELECTORS:")
            for btn in trade_buttons:
                text = btn['text']
                print(f"  button:has-text(\"{text}\")")
        else:
            print("\n⚠️ No obvious trade buttons found")
            print("📸 Check the screenshot for manual analysis")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if client:
            try:
                await client.close()
                print("🔌 Connection closed")
            except:
                pass

if __name__ == "__main__":
    asyncio.run(find_ui_elements())

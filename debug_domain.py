#!/usr/bin/env python3
"""Debug script to check domain configuration"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'pyquotex'))

from pyquotex.http.login import Login
from pyquotex.api import QuotexAPI

# Test the Login class directly
print("Testing Login class configuration:")
login = Login(None)
print(f"base_url: {login.base_url}")
print(f"https_base_url: {login.https_base_url}")

# Test QuotexAPI class
print("\nTesting QuotexAPI class:")
api = QuotexAPI("market-qx.pro", "<EMAIL>", "password", "pt")
print(f"host: {api.host}")
print(f"https_url: {api.https_url}")
print(f"wss_url: {api.wss_url}")

# Check if there are any imports or references that might be cached
print("\nChecking module locations:")
import pyquotex.http.login
print(f"Login module file: {pyquotex.http.login.__file__}")

import pyquotex.stable_api
print(f"Stable API module file: {pyquotex.stable_api.__file__}")

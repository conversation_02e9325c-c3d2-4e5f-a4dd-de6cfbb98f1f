# Technical Indicators Documentation in PyQuotex

## Table of Contents
1. [Introduction](#introduction)
2. [Available Timeframes](#available-timeframes)
3. [General Response Structure](#general-response-structure)
4. [Available Indicators](#available-indicators)
   - [RSI (Relative Strength Index)](#rsi)
   - [MACD (Moving Average Convergence Divergence)](#macd)
   - [Bollinger Bands](#bollinger-bands)
   - [Stochastic](#stochastic)
   - [ADX (Average Directional Index)](#adx)
   - [ATR (Average True Range)](#atr)
   - [Moving Averages (SMA and EMA)](#moving-averages)
   - [Ichimoku Cloud](#ichimoku-cloud)
5. [Real-Time Usage](#real-time-usage)
6. [Complete Examples](#complete-examples)

## Introduction

The PyQuotex library provides a comprehensive implementation of the most commonly used technical indicators in trading. Each indicator can be calculated across different timeframes and monitored in real-time.

## Available Timeframes

The following timeframes are available for all indicators:

```python
valid_timeframes = {
    60: "1 minute",
    300: "5 minutes",
    900: "15 minutes",
    1800: "30 minutes",
    3600: "1 hour",
    7200: "2 hours",
    14400: "4 hours",
    86400: "1 day"
}
```

## General Response Structure

All indicators return a similar structure that includes:

```python
{
    "indicator_values": [...],     # List of historical values
    "current": value,             # Current value of the indicator
    "timeframe": timeframe,       # Timeframe used
    "timestamps": [...],          # List of corresponding timestamps
    "history_size": size          # Number of historical values
}
```

## Available Indicators

### RSI
The Relative Strength Index measures the speed and magnitude of price movements.

```python
# Basic calculation
rsi = await client.calculate_indicator(
    asset="EURUSD",
    indicator="RSI",
    params={"period": 14},
    timeframe=300  # 5 minutes
)

# Response structure
{
    "rsi": [values...],
    "current": 65.45,
    "history_size": 100,
    "timeframe": 300,
    "timestamps": [timestamps...]
}
```

### MACD
The MACD is a trend-following indicator that shows the relationship between two moving averages.

```python
# Basic calculation
macd = await client.calculate_indicator(
    asset="EURUSD",
    indicator="MACD",
    params={
        "fast_period": 12,
        "slow_period": 26,
        "signal_period": 9
    },
    timeframe=900  # 15 minutes
)

# Response structure
{
    "macd": [values...],
    "signal": [values...],
    "histogram": [values...],
    "current": {
        "macd": 0.00125,
        "signal": 0.00100,
        "histogram": 0.00025
    },
    "timeframe": 900,
    "timestamps": [timestamps...]
}
```

### Bollinger Bands
Bollinger Bands are a volatility indicator that creates upper and lower bands around the price.

```python
# Basic calculation
bollinger = await client.calculate_indicator(
    asset="EURUSD",
    indicator="BOLLINGER",
    params={
        "period": 20,
        "std": 2
    },
    timeframe=1800  # 30 minutes
)

# Response structure
{
    "upper": [values...],
    "middle": [values...],
    "lower": [values...],
    "current": {
        "upper": 1.1050,
        "middle": 1.1000,
        "lower": 1.0950
    },
    "timeframe": 1800,
    "timestamps": [timestamps...]
}
```

### Stochastic
The Stochastic Oscillator is a momentum indicator that compares the closing price with the price range over a period.

```python
# Basic calculation
stochastic = await client.calculate_indicator(
    asset="EURUSD",
    indicator="STOCHASTIC",
    params={
        "k_period": 14,
        "d_period": 3
    },
    timeframe=3600  # 1 hour
)

# Response structure
{
    "k": [values...],
    "d": [values...],
    "current": {
        "k": 75.5,
        "d": 72.3
    },
    "timeframe": 3600,
    "timestamps": [timestamps...]
}
```

### ADX
The Average Directional Index measures the strength of a trend.

```python
# Basic calculation
adx = await client.calculate_indicator(
    asset="EURUSD",
    indicator="ADX",
    params={"period": 14},
    timeframe=7200  # 2 hours
)

# Response structure
{
    "adx": [values...],
    "plus_di": [values...],
    "minus_di": [values...],
    "current": {
        "adx": 25.5,
        "plus_di": 30.2,
        "minus_di": 20.1
    },
    "timeframe": 7200,
    "timestamps": [timestamps...]
}
```

### ATR
The Average True Range measures market volatility.

```python
# Basic calculation
atr = await client.calculate_indicator(
    asset="EURUSD",
    indicator="ATR",
    params={"period": 14},
    timeframe=14400  # 4 hours
)

# Response structure
{
    "atr": [values...],
    "current": 0.00123,
    "history_size": 100,
    "timeframe": 14400,
    "timestamps": [timestamps...]
}
```

### Moving Averages
Moving averages smooth price data to form a trend indicator.

```python
# SMA (Simple Moving Average)
sma = await client.calculate_indicator(
    asset="EURUSD",
    indicator="SMA",
    params={"period": 20},
    timeframe=86400  # 1 day
)

# EMA (Exponential Moving Average)
ema = await client.calculate_indicator(
    asset="EURUSD",
    indicator="EMA",
    params={"period": 20},
    timeframe=86400  # 1 day
)

# Response structure (same for both)
{
    "sma": [values...],  # or "ema" for EMA
    "current": 1.1000,
    "history_size": 100,
    "timeframe": 86400,
    "timestamps": [timestamps...]
}
```

### Ichimoku Cloud
The Ichimoku Cloud is an indicator that shows multiple levels of support and resistance.

```python
# Basic calculation
ichimoku = await client.calculate_indicator(
    asset="EURUSD",
    indicator="ICHIMOKU",
    params={
        "tenkan_period": 9,
        "kijun_period": 26,
        "senkou_b_period": 52
    },
    timeframe=3600  # 1 hour
)

# Response structure
{
    "tenkan": [values...],
    "kijun": [values...],
    "senkou_a": [values...],
    "senkou_b": [values...],
    "chikou": [values...],
    "current": {
        "tenkan": 1.1000,
        "kijun": 1.0990,
        "senkou_a": 1.1010,
        "senkou_b": 1.0980,
        "chikou": 1.0995
    },
    "timeframe": 3600,
    "timestamps": [timestamps...]
}
```

## Real-Time Usage

All indicators can be monitored in real-time using the `subscribe_indicator` function:

```python
async def on_indicator_update(data):
    print(f"Time: {data['time']}")
    print(f"Current value: {data['value']}")
    print(f"Historical values: {data['all_values']}")

# Subscribe to RSI updates
await client.subscribe_indicator(
    asset="EURUSD",
    indicator="RSI",
    params={"period": 14},
    callback=on_indicator_update,
    timeframe=300  # 5 minutes
)
```

## Complete Examples

### Example 1: Multi-Timeframe Analysis
```python
async def analyze_multi_timeframe():
    # Analyze RSI across multiple timeframes
    timeframes = [300, 900, 3600]  # 5m, 15m, 1h

    for tf in timeframes:
        rsi = await client.calculate_indicator(
            asset="EURUSD",
            indicator="RSI",
            params={"period": 14},
            timeframe=tf
        )
        print(f"RSI at {tf} seconds: {rsi['current']}")
```

### Example 2: Comprehensive Trend Analysis
```python
async def analyze_trend():
    # Get multiple indicators
    macd = await client.calculate_indicator(
        asset="EURUSD",
        indicator="MACD",
        timeframe=3600
    )

    adx = await client.calculate_indicator(
        asset="EURUSD",
        indicator="ADX",
        timeframe=3600
    )

    bb = await client.calculate_indicator(
        asset="EURUSD",
        indicator="BOLLINGER",
        timeframe=3600
    )

    # Analyze trend
    trend = {
        "macd_trend": "BULLISH" if macd["current"]["histogram"] > 0 else "BEARISH",
        "adx_strength": "STRONG" if adx["current"]["adx"] > 25 else "WEAK",
        "volatility": bb["current"]["upper"] - bb["current"]["lower"]
    }

    print("Trend analysis:", trend)
```

### Example 3: Real-Time Monitoring of Multiple Indicators
```python
async def monitor_multiple_indicators():
    async def on_update(data):
        print(f"Indicator: {data['indicator']}")
        print(f"Current value: {data['value']}")
        print(f"Timeframe: {data['timeframe']}")
        print("---")

    # Monitor RSI and MACD simultaneously
    await asyncio.gather(
        client.subscribe_indicator("EURUSD", "RSI", callback=on_update, timeframe=300),
        client.subscribe_indicator("EURUSD", "MACD", callback=on_update, timeframe=300)
    )
```
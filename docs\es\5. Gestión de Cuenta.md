# Gestión de Cuenta en PyQuotex

Esta documentación detalla las funcionalidades relacionadas con la gestión de cuenta en la API de PyQuotex.

## Configuración Inicial

```python
from pyquotex.stable_api import Quotex

# Inicializar el cliente
client = Quotex(
    email="<EMAIL>",
    password="tucontraseña",
    lang="es"  # Idioma por defecto: portugués (pt)
)

# Conectar con la API
await client.connect()
```

## Obtención del Perfil

Para obtener la información del perfil del usuario:

```python
async def obtener_perfil():
    profile = await client.get_profile()
    
    # Información disponible
    print(f"Usuario: {profile.nick_name}")
    print(f"Balance Demo: {profile.demo_balance}")
    print(f"Balance Real: {profile.live_balance}")
    print(f"ID: {profile.profile_id}")
    print(f"Avatar: {profile.avatar}")
    print(f"País: {profile.country_name}")
    print(f"Zona Horaria: {profile.offset}")
```

## Consulta de Balance

Para consultar el balance actual de la cuenta:

```python
async def consultar_balance():
    balance = await client.get_balance()
    print(f"Balance Actual: {balance}")
```

El balance mostrado corresponderá a la cuenta activa (demo o real).

## Recarga de Balance Demo

Para recargar el balance en la cuenta demo:

```python
async def recargar_balance_demo():
    # Recargar 5000 en la cuenta demo
    resultado = await client.edit_practice_balance(5000)
    print(resultado)
```

## Historial de Operaciones

Para obtener el historial de operaciones:

```python
async def obtener_historial():
    # Obtiene el historial de la cuenta activa
    historial = await client.get_history()
    
    for operacion in historial:
        print(f"ID: {operacion.get('ticket')}")
        print(f"Ganancia: {operacion.get('profitAmount')}")
        # Otros datos disponibles en el historial
```

También puedes consultar el resultado de una operación específica:

```python
async def consultar_operacion(operation_id):
    status, detalles = await client.get_result(operation_id)
    # status puede ser "win" o "loss"
    print(f"Resultado: {status}")
    print(f"Detalles: {detalles}")
```

## Cambio entre Cuentas Demo/Real

Para cambiar entre cuenta demo y real:

```python
# Cambiar a cuenta real
client.set_account_mode("REAL")

# Cambiar a cuenta demo
client.set_account_mode("PRACTICE")

# También puedes usar el método alternativo
client.change_account("REAL")  # o "PRACTICE"
```

### Notas Importantes:

1. La cuenta demo es el modo por defecto al inicializar el cliente.
2. Asegúrate de tener una conexión establecida antes de realizar operaciones.
3. El balance demo se puede recargar, pero el balance real no.
4. Las operaciones de cuenta real involucran dinero real, úsalas con precaución.
5. Siempre verifica el modo de cuenta activo antes de realizar operaciones.

## Manejo de Errores

Es recomendable implementar manejo de errores en las operaciones:

```python
async def operacion_segura():
    try:
        check_connect, message = await client.connect()
        if check_connect:
            # Realizar operaciones
            pass
        else:
            print(f"Error de conexión: {message}")
    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        await cliente.close()
```